package com.heal.etladapter.core.transformers;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.heal.etladapter.beans.*;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.transformers.HealKPITransformer;
import com.heal.etladapter.pojos.AdapterItem;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;


import java.util.*;

import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(value = {MockitoExtension.class})
public class HealKPITransformerTestKpiIdentifier {

    @InjectMocks
    @Spy
    HealKPITransformer testTransformer;
    @Mock
    private ConnectorKpiMaster connectorKpiMaster;

    @Mock
    private Map<Integer, String> kpiTypes = new HashMap<>();

    @Mock
    private Map<String, KpiDetail> healKpis = new HashMap<>();

    @Mock
    private Map<String, DomainHealInstance> instanceMap;

    @Mock
    private Map<String, DomainToHealKpiMappings> kpiMappings = new HashMap<>();

    @BeforeEach
    public void init() {

        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        testTransformer.setJdbcTemplate(jdbcTemplate);

        DomainHealInstance domainInstanceMap1 = DomainHealInstance.builder()
                .domainInstanceName("test_instance_2")
                .healAgentUid("test_agent_2")
                .healInstanceName("heal_instance_2")
                .build();

        // contains the map of src instance with heal agent and instance
        DomainHealInstance domainInstanceMap = DomainHealInstance.builder()
                .domainInstanceName("test_instance")
                .healAgentUid("test_agent")
                .healInstanceName("heal_instance")
                .build();



        List<DomainHealInstance> dList = new ArrayList<>();
        dList.add(domainInstanceMap);
        dList.add(domainInstanceMap1);

        when(connectorKpiMaster.fetchDomainEntityHealInstanceMapping("test", testTransformer.getJdbcTemplate())).thenReturn(dList);

        //
        List<KpiDetail> healKpiList = new ArrayList<>();
        KpiDetail healKpi = new KpiDetail();

        healKpi.setId(100);
        healKpi.setIdentifier("heal_kpi");
        healKpi.setName("heal kpi");
        healKpi.setKpiTypeId(String.valueOf(0));

        KpiDetail healKpi1 = new KpiDetail();

        healKpi1.setId(1000);
        healKpi1.setIdentifier("disk_free_space");
        healKpi1.setName("disk free space");
        healKpi1.setKpiGroupId(1);
        healKpi1.setKpiTypeId(String.valueOf(0));

        KpiDetail healKpi2 = new KpiDetail();

        healKpi2.setId(200);
        healKpi2.setIdentifier("heal_kpi_Availability");
        healKpi2.setName("heal kpi Availability");
        healKpi2.setKpiTypeId(String.valueOf(1));


        healKpiList.add(healKpi);
        healKpiList.add(healKpi1);
        healKpiList.add(healKpi2);


        when(connectorKpiMaster.getHealKPIs("Test", testTransformer.getJdbcTemplate())).thenReturn(healKpiList);
        //

        DomainToHealKpiMappings kpi = DomainToHealKpiMappings.builder()
                .id(1)
                .domain("test")
                .healKpiIdentifier("heal_kpi")
                .srcKpiIdentifier("test_kpi")
                .build();

        DomainToHealKpiMappings kpi1 = DomainToHealKpiMappings.builder()
                .id(2)
                .domain("test")
                .healKpiIdentifier("disk_free_space")
                .srcKpiIdentifier("free_space")
                .build();

        DomainToHealKpiMappings kpiAva = DomainToHealKpiMappings.builder()
                .id(3)
                .domain("test")
                .healKpiIdentifier("heal_kpi_Availability")
                .srcKpiIdentifier("availability")
                .build();

        List<DomainToHealKpiMappings> dKpiMap = new ArrayList<>();
        dKpiMap.add(kpi);
        dKpiMap.add(kpi1);
        dKpiMap.add(kpiAva);

        when(connectorKpiMaster.getDomainToHealKpiMappings("Test", testTransformer.getJdbcTemplate())).thenReturn(dKpiMap);


        //
            List<KPITypeDetails> kpiTypes = new ArrayList<>();

        KPITypeDetails kpiType = new KPITypeDetails();

        kpiType.setName("Core");
        kpiType.setSubTypeId(0);

        kpiTypes.add(kpiType);

        when(connectorKpiMaster.getKpiTypes(testTransformer.getJdbcTemplate())).thenReturn(kpiTypes);
        //

        Map<String, String> params = new HashMap<>();
        params.put(AdapterConstants.DOMAIN, "Test");
        params.put(AdapterConstants.ADAPTER_DEFAULT_INSTANCE, "TEST_INSTANCE");
        params.put(AdapterConstants.ADAPTER_DEFAULT_AGENT, "TEST_AGENT");

        testTransformer.setParameters(params);

        try {
            testTransformer.initialize();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void nonGroupKpiMethodTest() {

        MockitoAnnotations.initMocks(this);

        DomainKpi src = new DomainKpi();
        src.setKpiUid(10);
        src.setKpiName("test_kpi");
        src.setValue(10.0);
        src.setUpperThreshold(new Date());
        src.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
        src.setDomainInstanceId("test_instance");
        src.setCollectionInterval((int) (src.getUpperThreshold().getTime() - src.getLowerThreshold().getTime()) / 1000);
        AdapterItem item = new AdapterItem();
        item.setSourceItem(src);


        KpiDetail appsoneKpi = new KpiDetail();

        appsoneKpi.setId(100);
        appsoneKpi.setName("Heal_Kpi");
        appsoneKpi.setKpiTypeId("1");


        when(kpiTypes.get(1)).thenReturn("Core");

        try {
            KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = testTransformer.getKpiData(src, appsoneKpi);
            Assert.assertEquals(true, kpiData instanceof KPIAgentMessageProtos.KPIAgentMessage.KpiData);
            Assert.assertEquals(true, kpiData.getKpiUid() == 100);
            Assert.assertEquals(true, kpiData.getKpiName().equalsIgnoreCase("Heal_Kpi"));

        } catch (Exception e) {
            log.error("error occurred in nonGroupKPITest. Details : ", e);
        }
    }

    @Test
    public void groupKPIMethodTest() {

        MockitoAnnotations.initMocks(this);
        DomainKpi src = new DomainKpi();

        HashMap<String, String> groupKpis = new HashMap<>();

        groupKpis.put("EXECUTION", "1.2");
        groupKpis.put("ELAPSED_S", "3.4");
        groupKpis.put("LOCK_PER_EXEC_MS", "7.5");

        src.setKpiName("group_kpi");
        src.setKpiUid(1000);
        src.setValue(10.0);
        src.setGroupName("G1");
        src.setIsGroupKpi(true);
        src.setGroupKpis(groupKpis);
        src.setUpperThreshold(new Date());
        src.setLowerThreshold(new Date(src.getUpperThreshold().getTime() - 300));
        src.setDomainInstanceId("test_instance");

        src.setCollectionInterval((int) (src.getUpperThreshold().getTime() - src.getLowerThreshold().getTime()) / 1000);



        KpiDetail appsoneKpi = new KpiDetail();

        appsoneKpi.setId(100);
        appsoneKpi.setName("test_group_kpi");
        appsoneKpi.setKpiTypeId("1");
        appsoneKpi.setKpiGroupId(1);

        when(kpiTypes.get(1)).thenReturn("Core");

        try {
            Assert.assertEquals(true, testTransformer.getGroupKpiData(src, appsoneKpi) instanceof KPIAgentMessageProtos.KPIAgentMessage.KpiData);
            KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = testTransformer.getGroupKpiData(src, appsoneKpi);

            Assert.assertEquals(true, kpiData.getKpiUid() == 100);
            Assert.assertEquals(true, kpiData.getKpiGroupName().equals("G1"));
            Assert.assertEquals(true, kpiData.getGroupKpi().getPairsCount() == 3);

        } catch (Exception e) {
            log.error("error occurred in nonGroupKPITest. Details : ", e);
        }
    }

    @Test
    public void nonGroupKpiTransformTest(){


        // Add kpi data to one instance
        DomainKpi src = new DomainKpi();
        src.setKpiUid(10);
        src.setKpiName("test_kpi");
        src.setValue(10.0);
        src.setUpperThreshold(new Date());
        src.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
        src.setDomainInstanceId("test_instance");

        AdapterItem item = new AdapterItem();
        item.setSourceItem(src);

        // Add kpi data for other instance


        List<AdapterItem> items = new ArrayList<>();
        items.add(item);

        // contains the map of the src kpi and heal kpi
//        DomainToHealKpiMappings kpi = DomainToHealKpiMappings.builder()
//                .id(1)
//                .domain("test")
//                .healKpiIdentifier("heal_kpi")
//                .srcKpiIdentifier("test_kpi")
//                .build();
//
//        DomainHealInstance domainInstanceMap1 = DomainHealInstance.builder()
//                .domainInstanceName("test_instance_2")
//                .healAgentUid("test_agent_2")
//                .healInstanceName("heal_instance_2")
//                .build();

        // contains the map of src instance with heal agent and instance
//        DomainHealInstance domainInstanceMap = DomainHealInstance.builder()
//                .domainInstanceName("test_instance")
//                .healAgentUid("test_agent")
//                .healInstanceName("heal_instance")
//                .build();


//        when(kpiMappings.containsKey(src.getKpiName())).thenReturn(true);
//        when(kpiMappings.get("test_kpi")).thenReturn(kpi);
//        when(kpiTypes.get(1)).thenReturn("Core");
//        when(instanceMap.get("test_instance")).thenReturn(domainInstanceMap);
//        when(instanceMap.get("test_instance_2")).thenReturn(domainInstanceMap1);
//        when(instanceMap.containsKey("test_instance")).thenReturn(true);
//        when(instanceMap.containsKey("test_instance_2")).thenReturn(true);

//        KpiDetail healKpi = new KpiDetail();
//
//        healKpi.setId(100);
//        healKpi.setIdentifier("heal_kpi");
//        healKpi.setName("heal kpi");
//        healKpi.setKpiTypeId(String.valueOf(1));
//
//
//
//        when(healKpis.get("heal_kpi")).thenReturn(healKpi);

        List<KPIAgentMessageProtos.KPIAgentMessage> transformKpiData = testTransformer.transform(List.of(src));

        Assertions.assertTrue(transformKpiData.get(0).getAgentUid().equalsIgnoreCase("test_agent"));
        Assertions.assertTrue(transformKpiData.get(0).getInstances(0).getInstanceId().equalsIgnoreCase("heal_instance"));
        Assertions.assertTrue(transformKpiData.get(0).getAgentUid().equalsIgnoreCase("test_agent"));

        DomainKpi src2 = new DomainKpi();
        src2.setKpiUid(10);
        src2.setKpiName("test_kpi");
        src2.setValue(10.0);
        src2.setUpperThreshold(new Date());
        src2.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
        src2.setDomainInstanceId("test_instance_2");

        List<KPIAgentMessageProtos.KPIAgentMessage> transformKpiData1 = testTransformer.transform(List.of(src, src2));
        Assertions.assertEquals(2, transformKpiData1.size());

    }

    @Test
    public void groupKpiTransformTest(){

        DomainKpi src = new DomainKpi();

        HashMap<String, String> groupKpis = new HashMap<>();

        groupKpis.put("disk1", "400");
        groupKpis.put("disk2", "300");
        groupKpis.put("disk3", "500");

        src.setKpiUid(10);
        src.setKpiName("free_space");
        src.setGroupKpis(groupKpis);
        src.setGroupName("disk");
        src.setIsGroupKpi(true);
        src.setUpperThreshold(new Date());
        src.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
        src.setDomainInstanceId("test_instance");

        List<KPIAgentMessageProtos.KPIAgentMessage> transformKpiData = testTransformer.transform(List.of(src));

        Assertions.assertTrue(transformKpiData.get(0).getAgentUid().equalsIgnoreCase("test_agent"));
        Assertions.assertTrue(transformKpiData.get(0).getInstances(0).getInstanceId().equalsIgnoreCase("heal_instance"));
        Assertions.assertEquals(1000, transformKpiData.get(0).getInstances(0).getKpiDataList().get(0).getKpiUid());
        Assertions.assertTrue(transformKpiData.get(0).getInstances(0).getKpiDataList().get(0).getKpiName().equalsIgnoreCase("disk free space"));
        Assertions.assertTrue(transformKpiData.get(0).getInstances(0).getKpiDataList().get(0).getIsKpiGroup());
        Assertions.assertTrue(transformKpiData.get(0).getInstances(0).getKpiDataList().get(0).getKpiGroupName().equalsIgnoreCase("disk"));
        Assertions.assertEquals(3, transformKpiData.get(0).getInstances(0).getKpiDataList().get(0).getGroupKpi().getPairsCount());
    }

    @Test
    public void healKpiTransformerTest(){


        DomainKpi src = new DomainKpi();
        src.setKpiUid(10);
        src.setKpiName("test_kpi");
        src.setValue(10.0);
        src.setUpperThreshold(new Date());
        src.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
        src.setDomainInstanceId("test_instance");

        List<KPIAgentMessageProtos.KPIAgentMessage> transformKpiData = testTransformer.transform(List.of(src, (DomainKpi) null));
        Assertions.assertEquals(1, transformKpiData.size());


        DomainKpi src1 = new DomainKpi();
        src1.setKpiUid(10);
        src1.setKpiName("test_kpi");
        src1.setValue(10.0);
        src1.setUpperThreshold(new Date());
        src1.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));

        Assertions.assertTrue(transformKpiData.get(0).getAgentUid().equalsIgnoreCase("test_agent"));
        Assertions.assertTrue(transformKpiData.get(0).getInstances(0).getInstanceId().equalsIgnoreCase("heal_instance"));
        Assertions.assertTrue(transformKpiData.get(0).getAgentUid().equalsIgnoreCase("test_agent"));

        DomainKpi groupSrc = new DomainKpi();

        HashMap<String, String> groupKpis = new HashMap<>();

        groupKpis.put("disk1", "400");
        groupKpis.put("disk2", "300");
        groupKpis.put("disk3", "500");

        groupSrc.setKpiUid(10);
        groupSrc.setKpiName("free_space");
        groupSrc.setGroupKpis(groupKpis);
        groupSrc.setGroupName("disk");
        groupSrc.setIsGroupKpi(true);
        groupSrc.setUpperThreshold(new Date());
        groupSrc.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
        groupSrc.setDomainInstanceId("test_instance");

        List<KPIAgentMessageProtos.KPIAgentMessage> transformKpiDataGroup = testTransformer.transform(List.of(groupSrc));

        Assertions.assertTrue(transformKpiDataGroup.get(0).getAgentUid().equalsIgnoreCase("test_agent"));
        Assertions.assertTrue(transformKpiDataGroup.get(0).getInstances(0).getInstanceId().equalsIgnoreCase("heal_instance"));
        Assertions.assertEquals(1000, transformKpiDataGroup.get(0).getInstances(0).getKpiDataList().get(0).getKpiUid());
        Assertions.assertTrue(transformKpiDataGroup.get(0).getInstances(0).getKpiDataList().get(0).getKpiName().equalsIgnoreCase("disk free space"));
        Assertions.assertTrue(transformKpiDataGroup.get(0).getInstances(0).getKpiDataList().get(0).getIsKpiGroup());
        Assertions.assertTrue(transformKpiDataGroup.get(0).getInstances(0).getKpiDataList().get(0).getKpiGroupName().equalsIgnoreCase("disk"));
        Assertions.assertEquals(3, transformKpiDataGroup.get(0).getInstances(0).getKpiDataList().get(0).getGroupKpi().getPairsCount());

        DomainKpi srcAvailability = new DomainKpi();
        srcAvailability.setKpiUid(20);
        srcAvailability.setKpiName("availability");
        srcAvailability.setValue(0.0);
        srcAvailability.setUpperThreshold(new Date());
        srcAvailability.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300000));
        srcAvailability.setDomainInstanceId("test_instance");

        List<KPIAgentMessageProtos.KPIAgentMessage> transformKpiDataAvail = testTransformer.transform(List.of(srcAvailability));

        Assertions.assertEquals(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability, transformKpiDataAvail.get(0).getInstances(0).getKpiData(0).getKpiType());


    }


/*    @Test
    public void groupKPITestMappingBasedOnKpiIdentifier() {
        LogScanHealInstance instance = new LogScanHealInstance();
        instance.setHealAgentUid("1");
        instance.setHealInstanceName("Test");
        instance.setLogscanInstanceName("Logscan1");
        List<LogScanHealInstance> healInstnaces = new ArrayList<LogScanHealInstance>();
        healInstnaces.add(instance);
        when(logscanMasterRepository.fetchHealInstances()).thenReturn(healInstnaces);
        HealKPI kpi = new HealKPI();
        kpi.setGroup(true);
        kpi.setGroupName("G1");
        kpi.setId(100);
        kpi.setKpiName("Test KPI");
        kpi.setKpiIdentifier("identifier");
        List<HealKPI> healKpiList = new ArrayList<HealKPI>();
        healKpiList.add(kpi);
        when(logscanMasterRepository.getHealKPIs()).thenReturn(healKpiList);

        List<DomainToHealKPIMappings> tempMappings = new ArrayList<DomainToHealKPIMappings>();
        DomainToHealKPIMappings domain = new DomainToHealKPIMappings();
        domain.setDomain("TEST");
        domain.setHealKpiId("identifier");
        domain.setId(1);
        domain.setSrcKpiId(10);

        tempMappings.add(domain);
        when(logscanMasterRepository.getDomainToHealKPIMappings()).thenReturn(tempMappings);

        when(appsoneRepo.fetchKpiDetailByIdentifier("identifier")).thenReturn(KpiDetail.builder().id(1).build());
        LogScanLogAnalyzerKpi src = new LogScanLogAnalyzerKpi();
        src.setKpiUid(10);
        src.setValue(10.0);
        src.setGroupName("G1");
        src.setIsGroupKpi(true);
        src.setUpperThreshold(new Date());
        src.setLowerThreshold(new Date(src.getUpperThreshold().getTime()-300));
        src.setLogScanInstanceId("Logscan1");
        AdapterItem item = new AdapterItem();
        item.setSourceItem(src);
        try {
            testTransformer.initialize();
            testTransformer.transform(item);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error("error occurred in groupKPITestMappingBasedOnKpiIdentifier method. Details : ", e);
        }
        Assert.assertEquals(true, item.getDestItem() instanceof KPIAgentMessageProtos.KPIAgentMessage);
        KPIAgentMessageProtos.KPIAgentMessage kpiMessage = (KPIAgentMessage) item.getDestItem();
        Assert.assertEquals(true, kpiMessage.getAgentUid() == "1");
        Assert.assertEquals(true, kpiMessage.getInstances(0).getKpiData(0).getKpiGroupName() == "G1");
    }*/
}
