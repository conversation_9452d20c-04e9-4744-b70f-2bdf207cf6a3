package com.heal.etladapter.core.transformers;

import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.etladapter.beans.*;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.repo.redis.InstanceRepo;
import com.heal.etladapter.repo.redis.ServiceRepo;
import com.heal.etladapter.transformers.HealDomainEventTransformer;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(value = {MockitoExtension.class})
public class HealEventTransformerTest {

    @InjectMocks
    @Spy
    HealDomainEventTransformer transformer;

    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private ConnectorKpiMaster connectorKpiMaster;

    @Mock
    private ServiceRepo serviceRepo;
    @Mock
    private InstanceRepo instanceRepo;

    @Mock
    private Pattern pattern;
    @Mock
    private Map<String, KpiDetail> healKpis = new HashMap<>();

    @Mock
    private List<String> healServices = new ArrayList<>();

    @Mock
    private Map<String, CompInstClusterDetails> healInstances = new HashMap<>();

    @Mock
    private Map<String, DomainHealInstance> instanceMap = new HashMap<>();
    @Mock
    private Map<String, DomainToHealKpiMappings> kpiMappings = new HashMap<>();


    @BeforeEach
    public void init() {




        // Setting worker_parameters for the transformer

        Map<String, String> params = new HashMap<>();
        params.put(AdapterConstants.DOMAIN, "test");
        params.put(AdapterConstants.ADAPTER_DEFAULT_INSTANCE, "TEST_INSTANCE");
        params.put(AdapterConstants.ADAPTER_DEFAULT_AGENT, "TEST_AGENT");
        params.put(AdapterConstants.DATA_REGEX, "Condition<br><b>(?<metricname>.*?)<\\/b>.*?<b>(?<actualValue>.*?)<\\/b>.*?<b>(?<operationType>.*?)<\\/b>.*?<b>(?<threshold>.*?)<\\/b>");

        params.put(AdapterConstants.VALUE, "actualValue");
        params.put(AdapterConstants.THRESHOLD, "threshold");
        params.put(AdapterConstants.OPERATION_TYPE, "operationType");
        params.put(AdapterConstants.ACCOUNT_NAME, "test_account");
        transformer.setParameters(params);

        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        transformer.setJdbcTemplate(jdbcTemplate);

        String accountIdentifier = transformer.getParameters().get(AdapterConstants.ACCOUNT_NAME);
        // Create the Instance Map table list

        DomainHealInstance domainInstanceMap1 = DomainHealInstance.builder()
                .domainInstanceName("test_instance_2")
                .healAgentUid("test_agent_2")
                .healInstanceName("heal_instance_2")
                .build();

        // contains the map of src instance with heal agent and instance
        DomainHealInstance domainInstanceMap = DomainHealInstance.builder()
                .domainInstanceName("test_instance")
                .healAgentUid("test_agent")
                .healInstanceName("heal_instance")
                .build();



        List<DomainHealInstance> dList = new ArrayList<>();
        dList.add(domainInstanceMap);
        dList.add(domainInstanceMap1);

        when(connectorKpiMaster.fetchDomainEntityHealInstanceMapping("test", transformer.getJdbcTemplate())).thenReturn(dList);

        // Populate the service details same as the redis key output

        BasicEntity service = BasicEntity.builder()
                .identifier("test_service")
                .build();

        List<BasicEntity> basicEntities = new ArrayList<>();

        basicEntities.add(service);
        Mockito.when(serviceRepo.getAccountWiseServices(accountIdentifier)).thenReturn(basicEntities);




        // Populate the Instance details from the redis key

        CompInstClusterDetails compInst = CompInstClusterDetails.builder()
                .identifier("heal_instance")
                .build();

        List<CompInstClusterDetails> compList = new ArrayList<>();
        compList.add(compInst);

        Mockito.when(instanceRepo.getInstancesByAccount(accountIdentifier)).thenReturn(compList);

        //   Populate the Heal Kpis from appsone

        List<KpiDetail> healKpiList = new ArrayList<>();
        KpiDetail healKpi = new KpiDetail();

        healKpi.setId(100);
        healKpi.setIdentifier("heal_cpu_util");
        healKpi.setName("heal_cpu_util");
        healKpi.setKpiTypeId(String.valueOf(0));

        KpiDetail healKpi1 = new KpiDetail();

        healKpi1.setId(1000);
        healKpi1.setIdentifier("heal_memory_usage");
        healKpi1.setName("heal_memory_usage");
        healKpi1.setKpiTypeId(String.valueOf(0));

        KpiDetail healKpi2 = new KpiDetail();

        healKpi2.setId(200);
        healKpi2.setIdentifier("heal_kpi_Availability");
        healKpi2.setName("heal_kpi_Availability");
        healKpi2.setKpiTypeId(String.valueOf(1));

        healKpiList.add(healKpi);
        healKpiList.add(healKpi1);
        healKpiList.add(healKpi2);


        when(connectorKpiMaster.getHealKPIs("test", transformer.getJdbcTemplate())).thenReturn(healKpiList);
        // Populate the domain to heal kpi map object

        DomainToHealKpiMappings kpi = DomainToHealKpiMappings.builder()
                .id(1)
                .domain("test")
                .healKpiIdentifier("heal_cpu_util")
                .srcKpiIdentifier("test_cpu_util")
                .build();

        DomainToHealKpiMappings kpi1 = DomainToHealKpiMappings.builder()
                .id(2)
                .domain("test")
                .healKpiIdentifier("heal_memory_usage")
                .srcKpiIdentifier("test_memory_usage")
                .build();

        DomainToHealKpiMappings kpiAva = DomainToHealKpiMappings.builder()
                .id(3)
                .domain("test")
                .healKpiIdentifier("heal_kpi_Availability")
                .srcKpiIdentifier("test_kpi_Availability")
                .build();

        List<DomainToHealKpiMappings> dKpiMap = new ArrayList<>();
        dKpiMap.add(kpi);
        dKpiMap.add(kpi1);
        dKpiMap.add(kpiAva);

        when(connectorKpiMaster.getDomainToHealKpiMappings("test", transformer.getJdbcTemplate())).thenReturn(dKpiMap);


        //
        List<KPITypeDetails> kpiTypes = new ArrayList<>();

        KPITypeDetails kpiType = new KPITypeDetails();

        kpiType.setName("Core");
        kpiType.setSubTypeId(0);

        kpiTypes.add(kpiType);

        when(connectorKpiMaster.getKpiTypes(transformer.getJdbcTemplate())).thenReturn(kpiTypes);
        //

        try {
            transformer.initialize();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
