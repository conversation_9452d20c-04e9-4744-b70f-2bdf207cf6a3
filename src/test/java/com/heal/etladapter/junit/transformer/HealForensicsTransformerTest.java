package com.heal.etladapter.junit.transformer;

import com.appnomic.appsone.common.protbuf.A1EventProtos;
import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.common.protbuf.CommandResponseProtos;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.ByteString;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.pojos.ForensicItem;
import com.heal.etladapter.pojos.ForensicOutput;
import com.heal.etladapter.transformers.HealForensicsTransformer;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.zip.GZIPInputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive JUnit test class for HealForensicsTransformer.
 * 
 * Tests cover:
 * - Successful initialization and transformation scenarios
 * - Error handling for various failure cases
 * - Health metrics integration
 * - JSON serialization and compression/encoding functionality
 * - Edge cases and boundary conditions
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class HealForensicsTransformerTest {

    @Mock
    private ObjectMapper mockObjectMapper;

    @Mock
    private AdapterHealthMetrics mockHealthMetrics;

    @InjectMocks
    private HealForensicsTransformer transformer;

    private ForensicOutput testForensicOutput;
    private List<ForensicItem> testForensicItems;
    private CommandRequestProtos.CommandRequest testCommandRequest;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        // Create transformer with real ObjectMapper for most tests
        ObjectMapper realObjectMapper = new ObjectMapper();
        transformer = new HealForensicsTransformer(realObjectMapper);

        // Inject the mock health metrics using reflection
        setPrivateField(transformer, "healthMetrics", mockHealthMetrics);

        // Set up test data
        setupTestData();
    }

    /**
     * Helper method to set private fields using reflection
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * Sets up test data for forensic transformation tests
     */
    private void setupTestData() {
        // Create test forensic items
        testForensicItems = createTestForensicItems();
        
        // Create test command request
        testCommandRequest = createTestCommandRequest();
        
        // Create test forensic output
        testForensicOutput = ForensicOutput.builder()
                .commandRequest(testCommandRequest)
                .forensicItemList(testForensicItems)
                .build();
    }

    /**
     * Creates test forensic items (DomainKpi objects)
     */
    private List<ForensicItem> createTestForensicItems() {
        List<ForensicItem> items = new ArrayList<>();
        
        DomainKpi kpi1 = DomainKpi.builder()
                .kpiName("CPU_USAGE")
                .value(85.5)
                .domainInstanceId("server-001")
                .serviceName("web-service")
                .upperThreshold(new Date())
                .lowerThreshold(new Date(System.currentTimeMillis() - 300000))
                .collectionInterval(300)
                .build();
        
        DomainKpi kpi2 = DomainKpi.builder()
                .kpiName("MEMORY_USAGE")
                .value(70.2)
                .domainInstanceId("server-002")
                .serviceName("database-service")
                .upperThreshold(new Date())
                .lowerThreshold(new Date(System.currentTimeMillis() - 300000))
                .collectionInterval(300)
                .build();
        
        items.add(kpi1);
        items.add(kpi2);
        
        return items;
    }

    /**
     * Creates test command request with commands
     */
    private CommandRequestProtos.CommandRequest createTestCommandRequest() {
        CommandRequestProtos.Command command1 = CommandRequestProtos.Command.newBuilder()
                .setCommandJobId("job-001")
                .setCommandType("FORENSIC_COLLECTION")
                .setCommand("collect-forensics")
                .build();
        
        CommandRequestProtos.Command command2 = CommandRequestProtos.Command.newBuilder()
                .setCommandJobId("job-002")
                .setCommandType("FORENSIC_ANALYSIS")
                .setCommand("analyze-forensics")
                .build();
        
        return CommandRequestProtos.CommandRequest.newBuilder()
                .setAgentIdentifier("agent-001")
                .setAgentType("FORENSIC_AGENT")
                .setTriggerSource("MANUAL")
                .setTriggerTime(System.currentTimeMillis())
                .addSupervisorIdentifiers("supervisor-001")
                .addCommands(command1)
                .addCommands(command2)
                .build();
    }

    @Test
    public void testInitialize_Success() throws EtlAdapterException {
        // Given
        transformer.setClassName("HealForensicsTransformer");
        transformer.setJobId("test-job-001");
        transformer.setConnectorInstanceIdentifier("test-connector-001");
        Map<String, String> params = new HashMap<>();
        params.put("test-param", "test-value");
        transformer.setParameters(params);

        // When
        assertDoesNotThrow(() -> transformer.initialize());

        // Then
        // No exceptions should be thrown for successful initialization
        log.info("Initialize test completed successfully");
    }

    @Test
    public void testInitialize_NullObjectMapper_ThrowsException() throws Exception {
        // Given
        HealForensicsTransformer transformerWithNullMapper = new HealForensicsTransformer(null);
        setPrivateField(transformerWithNullMapper, "healthMetrics", mockHealthMetrics);
        transformerWithNullMapper.setClassName("HealForensicsTransformer");
        transformerWithNullMapper.setJobId("test-job-001");
        transformerWithNullMapper.setConnectorInstanceIdentifier("test-connector-001");

        // When & Then
        EtlAdapterException exception = assertThrows(EtlAdapterException.class,
                () -> transformerWithNullMapper.initialize());

        assertTrue(exception.getMessage().contains("ObjectMapper is not available"));
        verify(mockHealthMetrics).putInTransformerErrors(
                AdapterConstants.HEAL_FORENSIC_TRANSFORMER_INITIALIZATION_ERROR, 1);
    }

    @Test
    public void testTransform_Success() throws EtlAdapterException {
        // Given
        transformer.setClassName("HealForensicsTransformer");

        // When
        List<A1EventProtos.A1Event> result = transformer.transform(testForensicOutput);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Should have 2 A1Events for 2 commands

        // Verify health metrics
        verify(mockHealthMetrics).putInTransformerReceivedCount("HealForensicsTransformer", 2);
        
        // Verify A1Event structure
        A1EventProtos.A1Event firstEvent = result.get(0);
        assertEquals(Constants.FORENSIC_OUTPUT, firstEvent.getEventtype());
        assertNotNull(firstEvent.getEventdata());
        
        // Verify CommandResponse can be parsed from eventdata
        CommandResponseProtos.CommandResponse commandResponse = 
                CommandResponseProtos.CommandResponse.parseFrom(firstEvent.getEventdata());
        
        assertEquals("agent-001", commandResponse.getAgentIdentifier());
        assertEquals("FORENSIC_AGENT", commandResponse.getAgentType());
        assertEquals("job-001", commandResponse.getCommandJobId());
        assertEquals(0, commandResponse.getExitCode());
        assertFalse(commandResponse.getCmdOut().isEmpty()); // Should contain compressed data
        
        // Verify metadata
        assertTrue(commandResponse.getMetadataMap().containsKey("forensicItemCount"));
        assertEquals("2", commandResponse.getMetadataMap().get("forensicItemCount"));
    }

    @Test
    public void testTransform_EmptyForensicItems() throws EtlAdapterException {
        // Given
        ForensicOutput emptyForensicOutput = ForensicOutput.builder()
                .commandRequest(testCommandRequest)
                .forensicItemList(new ArrayList<>())
                .build();
        transformer.setClassName("HealForensicsTransformer");

        // When
        List<A1EventProtos.A1Event> result = transformer.transform(emptyForensicOutput);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Still 2 events for 2 commands, but with empty forensic data
        verify(mockHealthMetrics).putInTransformerReceivedCount("HealForensicsTransformer", 0);
    }

    @Test
    public void testTransform_JsonProcessingException() throws Exception {
        // Given
        HealForensicsTransformer transformerWithMockMapper = new HealForensicsTransformer(mockObjectMapper);
        setPrivateField(transformerWithMockMapper, "healthMetrics", mockHealthMetrics);
        transformerWithMockMapper.setClassName("HealForensicsTransformer");

        when(mockObjectMapper.writeValueAsString(any())).thenThrow(new JsonProcessingException("JSON error") {});

        // When & Then
        EtlAdapterException exception = assertThrows(EtlAdapterException.class,
                () -> transformerWithMockMapper.transform(testForensicOutput));

        assertTrue(exception.getMessage().contains("Error serializing forensic data"));
        verify(mockHealthMetrics).putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_ERROR, 1);
    }

    @Test
    public void testCompressAndBase64Encode_Success() throws Exception {
        // Given
        String testData = "This is test forensic data that should be compressed and encoded";
        
        // When
        String result = transformer.compressAndBase64Encode(testData);
        
        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // Verify it's valid Base64
        assertDoesNotThrow(() -> Base64.getDecoder().decode(result));
        
        // Verify we can decompress it back
        byte[] decodedBytes = Base64.getDecoder().decode(result);
        String decompressed = decompressGzip(decodedBytes);
        assertEquals(testData, decompressed);
    }

    @Test
    public void testSerializeForensicItems_Success() throws Exception {
        // When
        String result = transformer.serializeForensicItems(testForensicItems);
        
        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.contains("CPU_USAGE"));
        assertTrue(result.contains("MEMORY_USAGE"));
        assertTrue(result.contains("server-001"));
        assertTrue(result.contains("server-002"));
    }

    /**
     * Helper method to decompress GZIP data for verification
     */
    private String decompressGzip(byte[] compressedData) throws IOException {
        try (GZIPInputStream gzipIn = new GZIPInputStream(new ByteArrayInputStream(compressedData))) {
            return new String(gzipIn.readAllBytes(), "UTF-8");
        }
    }

    @Test
    public void testTransform_VerifyCompressionEfficiency() throws EtlAdapterException {
        // Given
        // Create larger forensic data to test compression
        List<ForensicItem> largeForensicItems = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            DomainKpi kpi = DomainKpi.builder()
                    .kpiName("TEST_KPI_" + i)
                    .value(Math.random() * 100)
                    .domainInstanceId("server-" + String.format("%03d", i))
                    .serviceName("service-" + (i % 10))
                    .upperThreshold(new Date())
                    .lowerThreshold(new Date(System.currentTimeMillis() - 300000))
                    .collectionInterval(300)
                    .build();
            largeForensicItems.add(kpi);
        }
        
        ForensicOutput largeForensicOutput = ForensicOutput.builder()
                .commandRequest(testCommandRequest)
                .forensicItemList(largeForensicItems)
                .build();
        
        transformer.setClassName("HealForensicsTransformer");

        // When
        List<A1EventProtos.A1Event> result = transformer.transform(largeForensicOutput);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(mockHealthMetrics).putInTransformerReceivedCount("HealForensicsTransformer", 100);
        
        // Verify compression worked (compressed data should be smaller than original JSON)
        A1EventProtos.A1Event firstEvent = result.get(0);
        CommandResponseProtos.CommandResponse commandResponse = 
                CommandResponseProtos.CommandResponse.parseFrom(firstEvent.getEventdata());
        
        String compressedData = commandResponse.getCmdOut();
        assertNotNull(compressedData);
        assertFalse(compressedData.isEmpty());
        
        // The compressed+encoded data should be significantly smaller than raw JSON
        // This is a rough check - actual compression ratio depends on data
        assertTrue(compressedData.length() > 0);
    }

    @Test
    public void testTransform_MultipleCommands_CreatesMultipleEvents() throws EtlAdapterException {
        // Given
        transformer.setClassName("HealForensicsTransformer");

        // When
        List<A1EventProtos.A1Event> result = transformer.transform(testForensicOutput);

        // Then
        assertEquals(2, result.size());
        
        // Verify each event corresponds to a different command
        Set<String> commandJobIds = new HashSet<>();
        for (A1EventProtos.A1Event event : result) {
            CommandResponseProtos.CommandResponse response = 
                    CommandResponseProtos.CommandResponse.parseFrom(event.getEventdata());
            commandJobIds.add(response.getCommandJobId());
        }
        
        assertEquals(2, commandJobIds.size());
        assertTrue(commandJobIds.contains("job-001"));
        assertTrue(commandJobIds.contains("job-002"));
    }

    @Test
    public void testTransform_NullForensicOutput_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, () -> transformer.transform(null));
    }

    @Test
    public void testTransform_NullCommandRequest_ThrowsException() {
        // Given
        ForensicOutput invalidOutput = ForensicOutput.builder()
                .commandRequest(null)
                .forensicItemList(testForensicItems)
                .build();

        // When & Then
        assertThrows(NullPointerException.class, () -> transformer.transform(invalidOutput));
    }

    @Test
    public void testTransform_NullForensicItemList_ThrowsException() {
        // Given
        ForensicOutput invalidOutput = ForensicOutput.builder()
                .commandRequest(testCommandRequest)
                .forensicItemList(null)
                .build();

        // When & Then
        assertThrows(NullPointerException.class, () -> transformer.transform(invalidOutput));
    }

    @Test
    public void testTransform_EmptyCommandsList() throws EtlAdapterException {
        // Given
        CommandRequestProtos.CommandRequest emptyCommandRequest =
                CommandRequestProtos.CommandRequest.newBuilder()
                .setAgentIdentifier("agent-001")
                .setAgentType("FORENSIC_AGENT")
                .setTriggerSource("MANUAL")
                .setTriggerTime(System.currentTimeMillis())
                .addSupervisorIdentifiers("supervisor-001")
                // No commands added
                .build();

        ForensicOutput emptyCommandsOutput = ForensicOutput.builder()
                .commandRequest(emptyCommandRequest)
                .forensicItemList(testForensicItems)
                .build();

        transformer.setClassName("HealForensicsTransformer");

        // When
        List<A1EventProtos.A1Event> result = transformer.transform(emptyCommandsOutput);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty()); // No events should be created for no commands
        verify(mockHealthMetrics).putInTransformerReceivedCount("HealForensicsTransformer", 2);
    }

    @Test
    public void testCompressAndBase64Encode_EmptyString() throws Exception {
        // Given
        String emptyData = "";

        // When
        String result = transformer.compressAndBase64Encode(emptyData);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty()); // Even empty string produces some compressed output

        // Verify we can decompress it back to empty string
        byte[] decodedBytes = Base64.getDecoder().decode(result);
        String decompressed = decompressGzip(decodedBytes);
        assertEquals(emptyData, decompressed);
    }

    @Test
    public void testCompressAndBase64Encode_LargeData() throws Exception {
        // Given
        StringBuilder largeData = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            largeData.append("This is line ").append(i).append(" of test data for compression testing.\n");
        }
        String testData = largeData.toString();

        // When
        String result = transformer.compressAndBase64Encode(testData);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // Verify compression efficiency - compressed should be much smaller
        assertTrue(result.length() < testData.length());

        // Verify we can decompress it back
        byte[] decodedBytes = Base64.getDecoder().decode(result);
        String decompressed = decompressGzip(decodedBytes);
        assertEquals(testData, decompressed);
    }

    @Test
    public void testTransform_VerifyMetadataContent() throws EtlAdapterException {
        // Given
        transformer.setClassName("HealForensicsTransformer");

        // When
        List<A1EventProtos.A1Event> result = transformer.transform(testForensicOutput);

        // Then
        A1EventProtos.A1Event firstEvent = result.get(0);
        CommandResponseProtos.CommandResponse commandResponse =
                CommandResponseProtos.CommandResponse.parseFrom(firstEvent.getEventdata());

        Map<String, String> metadata = commandResponse.getMetadataMap();

        // Verify metadata contains expected keys and values
        assertTrue(metadata.containsKey("forensicItemCount"));
        assertTrue(metadata.containsKey("commandJobId"));
        assertEquals("2", metadata.get("forensicItemCount"));
        assertTrue(metadata.get("commandJobId").startsWith("job-"));
    }

    @Test
    public void testTransform_VerifyCommandResponseFields() throws EtlAdapterException {
        // Given
        transformer.setClassName("HealForensicsTransformer");

        // When
        List<A1EventProtos.A1Event> result = transformer.transform(testForensicOutput);

        // Then
        A1EventProtos.A1Event firstEvent = result.get(0);
        CommandResponseProtos.CommandResponse commandResponse =
                CommandResponseProtos.CommandResponse.parseFrom(firstEvent.getEventdata());

        // Verify all required fields are set correctly
        assertEquals("supervisor-001", commandResponse.getSupervisorIdentifier());
        assertEquals("agent-001", commandResponse.getAgentIdentifier());
        assertEquals("FORENSIC_AGENT", commandResponse.getAgentType());
        assertEquals("FORENSIC_COLLECTION", commandResponse.getCommandType());
        assertEquals("collect-forensics", commandResponse.getCommand());
        assertEquals("", commandResponse.getStdErr()); // Should be empty
        assertEquals(0, commandResponse.getExitCode()); // Should be 0 for success
        assertTrue(commandResponse.getCommandStartTime() > 0);
        assertTrue(commandResponse.getCommandCompleteTime() > 0);
        assertFalse(commandResponse.getCmdOut().isEmpty()); // Should contain compressed data
    }

    @Test
    public void testSerializeForensicItems_WithNullValues() throws Exception {
        // Given
        List<ForensicItem> itemsWithNulls = new ArrayList<>();
        DomainKpi kpiWithNulls = DomainKpi.builder()
                .kpiName("TEST_KPI")
                .value(null) // null value
                .domainInstanceId("server-001")
                .serviceName(null) // null service name
                .upperThreshold(new Date())
                .lowerThreshold(null) // null lower threshold
                .build();
        itemsWithNulls.add(kpiWithNulls);

        // When
        String result = transformer.serializeForensicItems(itemsWithNulls);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.contains("TEST_KPI"));
        assertTrue(result.contains("server-001"));
        // Should handle null values gracefully in JSON
    }

    @Test
    public void testTransform_HealthMetricsIntegration() throws EtlAdapterException {
        // Given
        transformer.setClassName("HealForensicsTransformer");

        // When
        transformer.transform(testForensicOutput);

        // Then
        // Verify health metrics are called correctly
        verify(mockHealthMetrics, times(1)).putInTransformerReceivedCount(
                "HealForensicsTransformer", testForensicItems.size());

        // Verify no error metrics are recorded for successful transformation
        verify(mockHealthMetrics, never()).putInTransformerErrors(anyString(), anyInt());
    }
}
