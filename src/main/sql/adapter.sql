/*
 * Adapter Config
*/

SET SQL_SAFE_UPDATES = 0;

CREATE TABLE `mst_connector_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL,
  `description` varchar(256) NOT NULL,
  `configured` tinyint NOT NULL DEFAULT '0',
  `custom` tinyint NOT NULL,
  `is_template_exists` tinyint NOT NULL,
  `account_id` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `command_status` int DEFAULT NULL,
  `command_job_id` varchar(264) DEFAULT NULL,
  `is_command_executed` tinyint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_connector_account_mapping_idx` (`account_id`),
  CONSTRAINT `fk_connector_account_mapping_idx` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`)
) ENGINE=InnoDB;

CREATE TABLE `mst_connector_chains` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL,
  `identifier` varchar(256) NOT NULL,
   `status` int NOT NULL DEFAULT '1',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier_UNIQUE` (`identifier`)
) ENGINE=InnoDB

CREATE TABLE `mst_connector_instance_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(256) DEFAULT NULL,
  `identifier` varchar(256) DEFAULT NULL,
  `mst_connector_details_id` int NOT NULL,
  `connector_chain_id` int NOT NULL,
  `account_id` int NOT NULL,
   `status` int NOT NULL DEFAULT '1',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_mst_connector_details_id` (`mst_connector_details_id`),
  KEY `fk_chain_config_id` (`connector_chain_id`),
  CONSTRAINT `fk_chain_config_id` FOREIGN KEY (`connector_chain_id`) REFERENCES `mst_connector_chains` (`id`),
  CONSTRAINT `fk_mst_connector_details_id` FOREIGN KEY (`mst_connector_details_id`) REFERENCES `mst_connector_details` (`id`)
) ENGINE=InnoDB ;

CREATE TABLE `mst_connector_workers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL,
  `identifier` varchar(256) NOT NULL,
  `class_path` varchar(256) NOT NULL,
  `worker_type_id` int NOT NULL DEFAULT '0',
   `status` int NOT NULL DEFAULT '1',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE TABLE `mst_connector_chain_worker_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `connector_chain_id` int NOT NULL,
  `connector_worker_id` int NOT NULL,
  `status` int NOT NULL DEFAULT '1',
  `re_initialize` tinyint NOT NULL DEFAULT '1',
  `order` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `connector_chain_id_idx` (`connector_chain_id`),
  KEY `fk_connector_worker_id_idx` (`connector_worker_id`),
  CONSTRAINT `fk_connector_chain_id` FOREIGN KEY (`connector_chain_id`) REFERENCES `mst_connector_chains` (`id`),
  CONSTRAINT `fk_connector_worker_id` FOREIGN KEY (`connector_worker_id`) REFERENCES `mst_connector_workers` (`id`)
) ENGINE=InnoDB;

CREATE TABLE `mst_connector_worker_parameters` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL,
  `value` varchar(1024) NOT NULL,
  `connector_worker_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `status` int NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_connector_chain_worker_id_idx` (`connector_worker_id`),
  CONSTRAINT `fk_connector_chain_worker_id` FOREIGN KEY (`connector_worker_id`) REFERENCES `mst_connector_workers` (`id`)
) ENGINE=InnoDB;

CREATE TABLE `mst_connector_instance_worker_parameters_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `connector_instance_id` INT NOT NULL,
  `connector_worker_id` INT NOT NULL,
  `name` VARCHAR(256) NOT NULL,
  `value` VARCHAR(1024) NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `status` int NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  INDEX `connector_instance_id_idx` (`connector_instance_id` ASC) VISIBLE,
  INDEX `connector_worker_id_idx` (`connector_worker_id` ASC) VISIBLE,
  CONSTRAINT `connector_instance_id` FOREIGN KEY (`connector_instance_id`) REFERENCES `mst_connector_instance_details` (`id`),
  CONSTRAINT `connector_worker_id` FOREIGN KEY (`connector_worker_id`) REFERENCES `mst_connector_workers` (`id`)
) ENGINE=InnoDB ;

create table `domain_to_heal_kpi_mappings`
(
    id int not null auto_increment primary key,
    `domain` varchar(256) not null,
    `heal_kpi_identifier` varchar(256) not null,
    `src_kpi_identifier`  varchar(256) not null,
    `aggregation_level`  varchar(256)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

create table `domain_metric` (
`id` int not null auto_increment primary key,
`entity_type` varchar(256) not null,
`metric_identifier` varchar(256) not null,
`domain` varchar(256) not null
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

create table `domain_entities` (
`id` int not null auto_increment primary key,
`entity_identifier` varchar(256) not null,
`entity_type` varchar(256) not null,
`domain` varchar(256) not null
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

create table `domain_entity_heal_instance_mapper`
(
    `id` int not null auto_increment primary key,
    `domain_instance_name` varchar(512) not null,
    `heal_agent_uid` varchar(1024) not null,
    `heal_instance_name` varchar(1024) not null,
    `heal_service_name` varchar(1024) not null,
     `domain` varchar(256) not null
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `connector_tag_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `tag_type_id` int NOT NULL,
  `is_predefined` tinyint NOT NULL,
  `ref_table` varchar(64) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `ref_where_column_name` varchar(128) DEFAULT NULL,
  `ref_select_column_name` varchar(126) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_connector_tag_details_tag_type_id_idx` (`tag_type_id`),
  KEY `fk_connector_tag_details_account_id_idx` (`account_id`),
  CONSTRAINT `fk_connector_tag_details_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_connector_tag_details_tag_type_id` FOREIGN KEY (`tag_type_id`) REFERENCES `mst_sub_type` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `connector_tag_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tag_id` int NOT NULL,
  `object_id` int NOT NULL,
  `object_ref_table` varchar(256) NOT NULL,
  `tag_key` varchar(256) NOT NULL,
  `tag_value` text,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_connector_tag_mapping_account_id_idx` (`account_id`),
  KEY `fk_connector_tag_mapping_tag_id_idx` (`tag_id`),
  KEY `fk_connector_tag_mapping_tag_key_idx` (`tag_key`),
  KEY `fk_connector_tag_mapping_object_id_idx` (`object_id`),
  KEY `fk_connector_tag_mapping_object_ref_table_idx` (`object_ref_table`),
  CONSTRAINT `fk_connector_tag_mapping_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_connector_tag_mapping_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `connector_tag_details` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
