package com.heal.etladapter.core;

import com.heal.configuration.pojos.connectors.ConnectorChainConfiguration;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.beans.Chain;
import com.heal.etladapter.extractors.AbstractExtractor;
import com.heal.etladapter.loaders.AbstractLoader;
import com.heal.etladapter.repo.redis.AdapterRedisRepo;
import com.heal.etladapter.transformers.AbstractTransformer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class AdapterConfigBuilder<S, T, U> {

    @Autowired
    private BeanFactory factory;
    @Autowired
    private AdapterRedisRepo adapterRedisRepo;
    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private BeanCacheManager<S, T, U> beanCacheManager;

    public Chain<S, T, U> build(String jobId, String accountIdentifier, String instanceIdentifier) {
        try {
            ConnectorChainConfiguration chainConfiguration = adapterRedisRepo.getConnectorAdapterConfiguration(jobId,
                    accountIdentifier, instanceIdentifier);
            if (chainConfiguration == null) {
                log.error("Configuration  is unavailable for jobId:{}, accountIdentifier:{}, connector instance:{}. " +
                        "Will not be able to collect corresponding data.", jobId, accountIdentifier, instanceIdentifier);
                return null;
            }

            List<AbstractExtractor<S, T>> extractorList = chainConfiguration.getBasicChainableExtractor().stream().map(extractor -> {
                try {
                    return beanCacheManager.getOrCreateExtractor(jobId, accountIdentifier, instanceIdentifier, extractor);
                } catch (Exception e) {
                    log.error("Exception occurred while building adapter chain for jobId:{}, accountIdentifier:{}, connector instance:{}", jobId, accountIdentifier, instanceIdentifier, e);
                    return null;
                }
            }).filter(Objects::nonNull).toList();

            List<AbstractTransformer<T, U>> transformerList = chainConfiguration.getBasicChainableTransformer().stream().map(transformer -> {
                try {
                    return beanCacheManager.getOrCreateTransformer(jobId, accountIdentifier, instanceIdentifier, transformer);
                } catch (Exception e) {
                    log.error("Exception occurred while building adapter chain for jobId:{}, accountIdentifier:{}, connector instance:{}", jobId, accountIdentifier, instanceIdentifier, e);
                    return null;
                }
            }).filter(Objects::nonNull).toList();

            List<AbstractLoader<U>> loaderList = chainConfiguration.getBasicChainableLoader().stream().map(loader -> {
                try {
                    return beanCacheManager.getOrCreateLoader(jobId, accountIdentifier, instanceIdentifier, loader);
                } catch (Exception e) {
                    log.error("Exception occurred while building adapter chain for jobId:{}, accountIdentifier:{}, connector instance:{}", jobId, accountIdentifier, instanceIdentifier, e);
                    return null;
                }
            }).filter(Objects::nonNull).toList();

            return Chain.<S, T, U>builder()
                    .connectorInstanceIdentifier(instanceIdentifier)
                    .chainIdentifier(chainConfiguration.getChainIdentifier())
                    .status(chainConfiguration.isStatus())
                    .extractor(extractorList)
                    .transformer(transformerList)
                    .loader(loaderList)
                    .build();
        } catch (Exception e) {
            log.error("Exception occurred while building adapter chain for jobId:{}, accountIdentifier:{}, connector instance:{}", jobId, accountIdentifier, instanceIdentifier, e);
            return null;
        }
    }
}

