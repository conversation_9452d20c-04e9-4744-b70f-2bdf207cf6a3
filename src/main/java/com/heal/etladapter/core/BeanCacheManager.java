package com.heal.etladapter.core;

import com.heal.configuration.pojos.connectors.ConnectorWorker;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.config.DataSourceConfig;
import com.heal.etladapter.config.RedisConnectionConfig;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.extractors.AbstractExtractor;
import com.heal.etladapter.loaders.AbstractLoader;
import com.heal.etladapter.transformers.AbstractTransformer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class BeanCacheManager<S, T, U> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private AutowireCapableBeanFactory autowireCapableBeanFactory;
    @Autowired
    private DataSourceConfig dataSourceConfig;
    @Autowired
    private RedisConnectionConfig redisConnectionConfig;
    private final Map<String, AbstractExtractor<S, T>> extractorMap = new ConcurrentHashMap<>();
    private final Map<String, AbstractTransformer<T, U>> transformerMap = new ConcurrentHashMap<>();
    private final Map<String, AbstractLoader<U>> loaderMap = new ConcurrentHashMap<>();

    public AbstractExtractor<S, T> getOrCreateExtractor(String jobId, String accountIdentifier, String connectorInstance, ConnectorWorker chainableWorker) {
        String key = connectorInstance.concat("_").concat("_Extractor");

        return extractorMap.compute(key, (k, existingExtractor) -> {
            String classpath = chainableWorker.getClassPath();
            try {
                if (existingExtractor == null) {
                    log.info("No extractor available for key: {}, jobId:{}, accountIdentifier:{}. Creating a new bean.", key, jobId, accountIdentifier);
                    // Grab a fresh prototype
                    AbstractExtractor<S, T> extractor = (AbstractExtractor<S, T>) Class.forName(classpath).getDeclaredConstructor().newInstance();
                    autowireCapableBeanFactory.autowireBean(extractor);
                    extractor.setConnectorInstanceIdentifier(connectorInstance);
                    extractor.setJobId(jobId);
                    extractor.setParameters(chainableWorker.getParameters());
                    extractor.setClassName(classpath);
                    extractor.setOrder(chainableWorker.getOrder());

                    JdbcTemplate jdbcTemplate = dataSourceConfig.getOrCreateJdbcTemplate(connectorInstance, chainableWorker.getParameters());
                    if (jdbcTemplate == null) {
                        log.error("Failed to initialize the jdbcTemplate for extractor: {} of jobId:{}, accountIdentifier:{}, connector instance:{}", classpath, jobId, accountIdentifier, connectorInstance);
                        throw new EtlAdapterException("Failed to initialize the jdbcTemplate for extractor of JobId:" + jobId + ", accountIdentifier:" + accountIdentifier + ", connector instance:" + connectorInstance);
                    }
                    extractor.setJdbcTemplate(jdbcTemplate);

                    RedisTemplate<String, Object> redisTemplate = redisConnectionConfig.getRedisTemplateConnection(connectorInstance, chainableWorker.getParameters());
                    if (redisTemplate == null) {
                        log.error("Failed to initialize the redisTemplate for extractor: {} of jobId:{}, accountIdentifier:{}, connector instance:{}", classpath, jobId, accountIdentifier, connectorInstance);
                        throw new EtlAdapterException("Failed to initialize the redisTemplate for extractor of JobId:" + jobId + ", accountIdentifier:" + accountIdentifier + ", connector instance:" + connectorInstance);
                    }
                    extractor.setRedisTemplate(redisTemplate);

                    log.info("Extractor created for key: {}, classpath: {}, jobId:{}, accountIdentifier:{}", key, classpath, jobId, accountIdentifier);

                    extractor.initialize();
                    log.info("Extractor initialize() for key: {}, classpath: {}, jobId:{}, accountIdentifier:{}", key, classpath, jobId, accountIdentifier);

                    return extractor;
                }

                if (chainableWorker.isReInitialized()) {
                    log.info("Extractor reinitialize() for key: {}, classpath: {}, jobId:{}, accountIdentifier:{}", key, classpath, jobId, accountIdentifier);
                    existingExtractor.initialize();
                }

                return existingExtractor;
            } catch (Exception e) {
                log.error("Exception in creating extractor bean with classpath: {}, current extractor object: {} for key: {}, jobId:{}, accountIdentifier:{}", classpath, existingExtractor, key, jobId, accountIdentifier, e);
                healthMetrics.updateRmqMessagesDropCount();
                return existingExtractor;
            }
        });
    }

    public AbstractTransformer<T, U> getOrCreateTransformer(String jobId, String accountIdentifier, String connectorInstance, ConnectorWorker chainableWorker) {
        String key = connectorInstance.concat("_").concat("_Transformer");

        return transformerMap.compute(key, (k, existingTransformer) -> {
            String classpath = chainableWorker.getClassPath();
            try {
                if (existingTransformer == null) {
                    log.info("No transformer available for key:{}, jobId:{}, accountIdentifier:{}. Creating a new bean.", key, jobId, accountIdentifier);
                    // Grab a fresh prototype
                    AbstractTransformer<T, U> transformer = (AbstractTransformer<T, U>) Class.forName(classpath).getDeclaredConstructor().newInstance();
                    autowireCapableBeanFactory.autowireBean(transformer);
                    transformer.setConnectorInstanceIdentifier(connectorInstance);
                    transformer.setJobId(jobId);
                    transformer.setOrder(chainableWorker.getOrder());
                    transformer.setClassName(classpath);
                    transformer.setParameters(chainableWorker.getParameters());

                    JdbcTemplate jdbcTemplate = dataSourceConfig.getOrCreateJdbcTemplate(connectorInstance, chainableWorker.getParameters());
                    if (jdbcTemplate == null) {
                        log.error("Failed to initialize the jdbcTemplate for transformer: {} of jobId:{}, accountIdentifier:{}, connector instance:{}", classpath, jobId, accountIdentifier, connectorInstance);
                        throw new EtlAdapterException("Failed to initialize the jdbcTemplate for transformer of JobId:" + jobId + ", accountIdentifier:" + accountIdentifier + ", connector instance:" + connectorInstance);
                    }
                    transformer.setJdbcTemplate(jdbcTemplate);

                    RedisTemplate<String, Object> redisTemplate = redisConnectionConfig.getRedisTemplateConnection(connectorInstance, chainableWorker.getParameters());
                    if (redisTemplate == null) {
                        log.error("Failed to initialize the redisTemplate for transformer: {} of jobId:{}, accountIdentifier:{}, connector instance:{}", classpath, jobId, accountIdentifier, connectorInstance);
                        throw new EtlAdapterException("Failed to initialize the redisTemplate for transformer of JobId:" + jobId + ", accountIdentifier:" + accountIdentifier + ", connector instance:" + connectorInstance);
                    }
                    transformer.setRedisTemplate(redisTemplate);

                    log.info("Transformer created for key: {}, jobId:{}, accountIdentifier:{}, classpath: {}", key, jobId, accountIdentifier, classpath);
                    transformer.initialize();
                    log.info("Transformer initialize() for key: {}, jobId:{}, accountIdentifier:{}, classpath: {}", key, jobId, accountIdentifier, classpath);
                    return transformer;
                }

                if (chainableWorker.isReInitialized()) {
                    log.info("Transformer reinitialize() for key: {}, jobId:{}, accountIdentifier:{}, classpath: {}", key, jobId, accountIdentifier, classpath);
                    existingTransformer.initialize();
                }

                return existingTransformer;
            } catch (Exception e) {
                log.error("Exception in creating transformer bean with classpath: {}, Current transformer object: {} for key: {}, jobId:{}, accountIdentifier:{}"
                        , classpath, existingTransformer, key, jobId, accountIdentifier, e);
                healthMetrics.updateRmqMessagesDropCount();
                return existingTransformer;
            }
        });
    }

    public AbstractLoader<U> getOrCreateLoader(String jobId, String accountIdentifier, String connectorInstance, ConnectorWorker chainableWorker) {
        String key = connectorInstance.concat("_").concat("_Loader");

        return loaderMap.compute(key, (k, existingLoader) -> {
            String classpath = chainableWorker.getClassPath();
            try {
                if (existingLoader == null) {
                    log.info("No loader available for key: {}, jobId:{}, accountIdentifier:{}. Creating a new bean.", key, jobId, accountIdentifier);
                    // Grab a fresh prototype
                    AbstractLoader<U> loader = (AbstractLoader<U>) Class.forName(classpath).getDeclaredConstructor().newInstance();
                    autowireCapableBeanFactory.autowireBean(loader);
                    loader.setConnectorInstanceIdentifier(connectorInstance);
                    loader.setJobId(jobId);
                    loader.setOrder(chainableWorker.getOrder());
                    loader.setClassName(classpath);
                    loader.setParameters(chainableWorker.getParameters());

                    JdbcTemplate jdbcTemplate = dataSourceConfig.getOrCreateJdbcTemplate(connectorInstance, chainableWorker.getParameters());
                    if (jdbcTemplate == null) {
                        log.error("Failed to initialize the jdbcTemplate for loader: {} of jobId:{}, accountIdentifier:{}, connector instance:{}", classpath, jobId, accountIdentifier, connectorInstance);
                        throw new EtlAdapterException("Failed to initialize the jdbcTemplate for loader of JobId:" + jobId + ", accountIdentifier:" + accountIdentifier + ", connector instance:" + connectorInstance);
                    }
                    loader.setJdbcTemplate(jdbcTemplate);

                    RedisTemplate<String, Object> redisTemplate = redisConnectionConfig.getRedisTemplateConnection(connectorInstance, chainableWorker.getParameters());
                    if (redisTemplate == null) {
                        log.error("Failed to initialize the redisTemplate for loader: {} of jobId:{}, accountIdentifier:{}, connector instance:{}", classpath, jobId, accountIdentifier, connectorInstance);
                        throw new EtlAdapterException("Failed to initialize the redisTemplate for loader of JobId:" + jobId + ", accountIdentifier:" + accountIdentifier + ", connector instance:" + connectorInstance);
                    }
                    loader.setRedisTemplate(redisTemplate);

                    log.info("Loader created for key: {}, classpath: {}, jobId:{}, accountIdentifier:{}", key, classpath, jobId, accountIdentifier);
                    loader.initialize();
                    log.info("Loader initialize() for key: {}, classpath: {}, jobId:{}, accountIdentifier:{}", key, classpath, jobId, accountIdentifier);
                    return loader;
                }

                if (chainableWorker.isReInitialized()) {
                    log.info("Loader reinitialize() for key: {}, classpath: {}, jobId:{}, accountIdentifier:{}", key, classpath, jobId, accountIdentifier);
                    existingLoader.initialize();
                }

                return existingLoader;
            } catch (Exception e) {
                log.error("Exception in creating loader bean with classpath: {}, Current loader object: {} for key: {}, jobId:{}, accountIdentifier:{}", classpath, existingLoader, key, jobId, accountIdentifier, e);
                healthMetrics.updateRmqMessagesDropCount();
                return existingLoader;
            }
        });
    }
}

