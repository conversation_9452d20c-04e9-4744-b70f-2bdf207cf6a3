package com.heal.etladapter.core;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.beans.Chain;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.extractors.AbstractExtractor;
import com.heal.etladapter.loaders.AbstractLoader;
import com.heal.etladapter.transformers.AbstractTransformer;
import com.heal.etladapter.utility.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AdapterETLExecutor<S, T, U> {

    @Autowired
    AdapterHealthMetrics healthMetrics;

    public void execute(Chain<S, T, U> chain, String jobId, String accountIdentifier, long startTime, long endTime, S rmqInputObject) {
        try {
            String strLowerThreshold = DateHelper.getDateInGMT(startTime);
            String strUpperThreshold = DateHelper.getDateInGMT(endTime);

            log.info("Waking up at:{} to execute chain:{} for jobId:{}, accountIdentifier:{} of data between startTime:{} and endTime:{}",
                    strUpperThreshold, chain.getChainIdentifier(), jobId, accountIdentifier, strLowerThreshold, strUpperThreshold);

            long start = System.currentTimeMillis();

            List<AbstractExtractor<S, T>> extractor = chain.getExtractor();
            if (extractor == null || extractor.isEmpty()) {
                log.error("Invalid extractor list for chain:{}, jobId:{}, accountIdentifier;{}. Data cannot be processed. Adapter chain details:{}", chain.getChainIdentifier(), jobId, accountIdentifier, chain);
                healthMetrics.updateAdapterExceptions();
                return;
            }

            List<AbstractTransformer<T, U>> transformer = chain.getTransformer();
            if (transformer == null || transformer.isEmpty()) {
                log.error("Invalid transformer list for chain:{}, jobId:{}, accountIdentifier:{}. Data cannot be processed. Adapter chain details:{}", chain.getChainIdentifier(), jobId, accountIdentifier, chain);
                healthMetrics.updateAdapterExceptions();
                return;
            }

            List<AbstractLoader<U>> loader = chain.getLoader();
            if (loader == null || loader.isEmpty()) {
                log.error("Invalid loader list for chain:{}, jobId:{}, accountIdentifier:{}. Data cannot be processed. Adapter chain details:{}", chain.getChainIdentifier(), jobId, accountIdentifier, chain);
                healthMetrics.updateAdapterExceptions();
                return;
            }

            T extractedItems = null;
            for (AbstractExtractor<S, T> ex : chain.getExtractor()) {
                try {
                    extractedItems = ex.extract(startTime, endTime, rmqInputObject);
                } catch (Exception e) {
                    log.error("Error in executing extractor:{}, jodId:{}, accountIdentifier:{} with lowerThreshold:{}, upperThreshold:{} and items:{}. " +
                            "Data cannot be processed further.", ex.getClassName(), jobId, accountIdentifier, startTime, endTime, extractedItems, e);
                    throw new EtlAdapterException(e.getMessage());
                }
            }

            for (AbstractTransformer<T, U> tn : chain.getTransformer()) {
                try {
                    U transformedItems = tn.transform(extractedItems);

                    chain.getLoader().stream()
                            .filter(l -> l.getOrder() == tn.getOrder())
                            .findFirst()
                            .ifPresent(l -> l.load(transformedItems));
                } catch (Exception e) {
                    log.error("Error in executing transformer:{} with lowerThreshold:{}, upperThreshold:{} and items:{}. " +
                            "Data cannot be processed further.", tn.getClassName(), startTime, endTime, extractedItems, e);
                    throw new EtlAdapterException(e.getMessage());
                }
            }
            log.info("Overall Execution completed for jobId:{}, accountIdentifier:{}, connector instance:{}, chain{} in{} ms", jobId, accountIdentifier, chain.getConnectorInstanceIdentifier(), chain.getChainIdentifier(), System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.info("Exception in executing data collection for jobId:{}, accountIdentifier:{}, connector instance:{}, chain{}", jobId, accountIdentifier, chain.getConnectorInstanceIdentifier(), chain, e);
            healthMetrics.updateAdapterExceptions();
        }
    }
}
