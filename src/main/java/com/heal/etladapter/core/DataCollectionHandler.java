package com.heal.etladapter.core;

import com.heal.configuration.protbuf.ScheduledJobProtos;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.beans.Chain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class DataCollectionHandler<S, T, U> {

    @Autowired
    AdapterConfigBuilder<S, T, U> adapterConfigBuilder;
    @Autowired
    AdapterETLExecutor<S, T, U> executor;
    @Autowired
    AdapterHealthMetrics healthMetrics;

    @Async("ThreadPoolTaskExecutor")
    public void process(ScheduledJobProtos.ScheduledJob scheduledJob) {
        long st = System.currentTimeMillis();
        String accountIdentifier = scheduledJob.getAccountIdentifier();
        String jobId = scheduledJob.getMetadataOrDefault("jobId", "NA");
        try {
            if (accountIdentifier.trim().isEmpty()) {
                healthMetrics.updateRmqMessagesDropCount();
                log.error("Invalid account identifier in scheduler details for jobId:{}. Dropping the data point. Scheduler details:{}", jobId, scheduledJob);
                return;
            }

            List<ScheduledJobProtos.Arguments> arguments = scheduledJob.getSchedulerArgumentsList();
            ScheduledJobProtos.Arguments instanceArg = arguments.stream()
                    .filter(arg -> arg.getName().equals("connector_instance"))
                    .findAny().orElse(null);
            if (instanceArg == null) {
                healthMetrics.updateRmqMessagesDropCount();
                log.error("'connector_instance' unavailable in scheduler arguments. Dropping the data point for jobId:{}, accountIdentifier:{}. Scheduler details: {}", jobId, accountIdentifier, scheduledJob);
                return;
            }
            String instanceIdentifier = instanceArg.getValue();
            Chain<S, T, U> chain = adapterConfigBuilder.build(jobId, accountIdentifier, instanceIdentifier);
            if (chain == null) {
                log.info("Adapter configuration is invalid, jobId:{}, accountIdentifier:{}, connector instance:{}", jobId, accountIdentifier, instanceIdentifier);
                healthMetrics.updateRmqMessagesDropCount();
                return;
            }

            if (!chain.isStatus()) {
                healthMetrics.updateRmqMessagesDropCount();
                log.info("Chain {} is disabled for jobId:{}, accountIdentifier:{}, connector instance:{}", jobId, accountIdentifier, instanceIdentifier, chain.getChainIdentifier());
                return;
            }

            log.info("Workers initialized for jobId:{}, accountIdentifier:{}, connector instance:{}", jobId, accountIdentifier, instanceIdentifier);
            log.trace("Initialized jobId:{}, accountIdentifier:{}, connector instance:{}, chainDetails: {}", jobId, accountIdentifier, instanceIdentifier, chain);

            executor.execute(chain, jobId, accountIdentifier, scheduledJob.getStartTime(), scheduledJob.getEndTime(), null);
        } catch (Exception e) {
            log.error("Exception occurred in scheduling data collection for jobId:{}, accountIdentifier:{}", jobId, accountIdentifier, e);
            healthMetrics.updateAdapterExceptions();
        } finally {
            log.info("Time taken to complete the jobId:{}, accountIdentifier:{} is {}ms.", jobId, accountIdentifier, System.currentTimeMillis() - st);
        }
    }
}
