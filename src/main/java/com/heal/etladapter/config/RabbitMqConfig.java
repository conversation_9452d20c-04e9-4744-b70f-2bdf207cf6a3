package com.heal.etladapter.config;

import com.heal.etladapter.service.EventReceiver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.RabbitConnectionFactoryBean;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Objects;

@Slf4j
@Configuration
public class RabbitMqConfig {

    @Value("${connector.message.queue.name}")
    public String connectorMessagesQueue;

    public static final boolean IS_QUEUE_DURABLE = true;
    public static final boolean IS_QUEUE_EXCLUSIVE = false;
    public static final boolean QUEUE_AUTO_DELETE = false;

    @DependsOn("queueNameInitializer")
    @Bean
    public Queue createInputQueue() {
        return new Queue(connectorMessagesQueue, IS_QUEUE_DURABLE, IS_QUEUE_EXCLUSIVE, QUEUE_AUTO_DELETE, null);
    }

    @Bean
    public RabbitTemplate rabbitTemplate(CachingConnectionFactory connectionFactory) {
        return new RabbitTemplate(connectionFactory);
    }

    @Bean
    public EventReceiver receiver() {
        return new EventReceiver();
    }

    @Bean
    public CachingConnectionFactory createSslConnectionFactory(RabbitProperties rabbitProperties) {
        log.info("RabbitProperties: {}, {}, {}", rabbitProperties.getAddresses(), rabbitProperties.getUsername(),
                rabbitProperties.getPassword());

        RabbitConnectionFactoryBean factory = new RabbitConnectionFactoryBean();
        if (rabbitProperties.getUsername() != null) {
            factory.setUsername(rabbitProperties.getUsername());
        }
        if (rabbitProperties.getPassword() != null && !rabbitProperties.getPassword().trim().isEmpty()) {
            factory.setPassword(new String(Base64.getDecoder().decode(rabbitProperties.getPassword().trim()), StandardCharsets.UTF_8));
        }

        RabbitProperties.Ssl ssl = rabbitProperties.getSsl();
        if (ssl.getEnabled()) {
            factory.setUseSSL(true);
            factory.setEnableHostnameVerification(false);
            factory.setSslAlgorithm(ssl.getAlgorithm());
        }
        factory.setAutomaticRecoveryEnabled(true);
        factory.afterPropertiesSet();

        CachingConnectionFactory connectionFactory = null;
        try {
            connectionFactory = new CachingConnectionFactory(Objects.requireNonNull(factory.getRabbitConnectionFactory()));
            connectionFactory.setAddresses(rabbitProperties.getAddresses());
        } catch (Exception e) {
//            metrics.updateErrors();
            log.error("Exception occurred while creating ConnectionFactory. RabbitMQ Addresses:{}, Username:{}, IsSSL:{}, Algorithm:{}",
                    rabbitProperties.getAddresses(), rabbitProperties.getUsername(),
                    rabbitProperties.getSsl().getEnabled(), rabbitProperties.getSsl().getAlgorithm(), e);
        }

        return connectionFactory;
    }

    @Bean("queueNameInitializer")
    public CommandLineRunner commandLineRunner(ApplicationContext ctx) {
        return args -> {
            log.info("Number of arguments:{}.", Arrays.stream(args).count());
            for (String arg : args) {
                log.info("Queue name: {}", arg);
                connectorMessagesQueue = arg;
            }
        };
    }
}
