package com.heal.etladapter.extractors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Data
@Component
public abstract class AbstractExtractor<S, T> {

    protected String connectorInstanceIdentifier;
    protected String jobId;
    protected Map<String, String> parameters;
    protected String className;
    protected int order;
    protected RedisTemplate<String, Object> redisTemplate;
    protected JdbcTemplate jdbcTemplate;

    public abstract void initialize() throws Exception;

    public abstract T extract(long from, long to, S items);
}
