package com.heal.etladapter.extractors;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.HttpConnectionConfiguration;
import com.heal.etladapter.service.HttpConnection;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class HttpExtractor<S, T> extends AbstractExtractor<S, T> {

    @Autowired
    protected ObjectMapper objectMapper;
    @Autowired
    protected AdapterHealthMetrics healthMetrics;
    protected HttpConnection httpConnection;

    @Override
    public void initialize() throws Exception {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);

        if (this.httpConnection == null) {
            HttpConnectionConfiguration httpConnectionConfiguration = HttpConnectionConfiguration.builder()
                    .httpConnectionRequestTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_REQUEST_TIMEOUT, "5000")))
                    .httpClientConnectionTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_TIMEOUT, "5000")))
                    .httpSocketTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_SOCKET_TIMEOUT, "5000")))
                    .connectionKeepAliveTime(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_KEEP_ALIVE_TIME, "30000")))
                    .maxConnections(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS, "50")))
                    .maxConnectionsPerRoute(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS_PER_ROUTE, "20")))
                    .disableSslValidation(Boolean.parseBoolean(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_DISABLE_SSL_VALIDATION, "true")))
                    .build();
            log.info("Initializing http connection with configuration: {}, jobId:{}, connector instance:{}", httpConnectionConfiguration, jobId, connectorInstanceIdentifier);
            httpConnection = new HttpConnection(httpConnectionConfiguration, healthMetrics);
        }

        CloseableHttpClient client = httpConnection.getHttpConnection();
        if (client == null) {
            healthMetrics.putInExtractorHttpCallDetails(AdapterConstants.HTTP_CONNECTION_ERROR, 1);
            throw new EtlAdapterException("Unable to create HttpClient instance for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
    }
}

