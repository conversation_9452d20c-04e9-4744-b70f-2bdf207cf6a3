package com.heal.etladapter.pojos;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForensicOutput {

    private CommandRequestProtos.CommandRequest commandRequest;
    private List<ForensicItem> forensicItemList;
}
