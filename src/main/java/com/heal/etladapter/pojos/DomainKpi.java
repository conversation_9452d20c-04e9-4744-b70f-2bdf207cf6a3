package com.heal.etladapter.pojos;

import com.heal.configuration.enums.KpiType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DomainKpi implements ForensicItem {

    protected String serviceName;
    protected Double value;
    protected String groupName;
    protected Boolean isGroupKpi;
    protected Integer collectionInterval;
    protected Integer kpiUid;
    private String kpiName;
    private Date lowerThreshold;
    private Date upperThreshold;
    private String errorPattern;
    private String domainInstanceId;
    private String healInstance;
    private Double entryMetric;
    private Double exitMetric;
    private KpiType kpiType;
    private Map<String, String> groupKpis;
    private String kpiGroupName;

    @Override
    public String toString() {
        return "DomainKpi{" +
                "kpiName='" + kpiName + '\'' +
                ", lowerThreshold=" + lowerThreshold +
                ", upperThreshold=" + upperThreshold +
                ", errorPattern='" + errorPattern + '\'' +
                ", domainInstanceId='" + domainInstanceId + '\'' +
                ", healInstance='" + healInstance + '\'' +
                ", entryMetric=" + entryMetric +
                ", exitMetric=" + exitMetric +
                ", kpiType=" + kpiType +
                ", groupKpis=" + groupKpis +
                ", kpiGroupName='" + kpiGroupName + '\'' +
                ", serviceName='" + serviceName + '\'' +
                ", value=" + value +
                ", groupName='" + groupName + '\'' +
                ", isGroupKpi=" + isGroupKpi +
                ", collectionInterval=" + collectionInterval +
                ", kpiUid=" + kpiUid +
                '}';
    }
}
