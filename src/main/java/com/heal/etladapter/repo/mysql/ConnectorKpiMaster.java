package com.heal.etladapter.repo.mysql;

import com.heal.etladapter.beans.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class ConnectorKpiMaster {

    public List<KpiDetail> getHealKPIs(String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select mkd.id, name, description, data_type, is_custom, status, kpi_type_id, measure_units, cluster_operation," +
                    " created_time, updated_time, user_details_id, account_id, kpi_group_id, identifier, value_type, rollup_operation," +
                    " cluster_aggregation_type, instance_aggregation_type, is_informative, is_computed, cron_expression, delta_per_sec," +
                    " reset_delta_value from mst_kpi_details mkd join domain_to_heal_kpi_mappings on mkd.identifier = heal_kpi_identifier and domain = ?";

            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiDetail.class), domain);
        } catch (EmptyResultDataAccessException e) {
            log.error("Domain to HEAL KPI mapping unavailable for domain {}", domain);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Exception while fetching domain to HEAL KPI mapping for connector {}", domain, e);
            return Collections.emptyList();
        }
    }

    public List<DomainToHealKpiMappings> getDomainToHealKpiMappings(String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select id, domain, heal_kpi_identifier, src_kpi_identifier, aggregation_level from domain_to_heal_kpi_mappings where domain = ?";

            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(DomainToHealKpiMappings.class), domain);
        } catch (Exception e) {
            log.error("Exception while fetching domain to HEAL KPI mapping!", e);
            return Collections.emptyList();
        }
    }

    public KpiDetail fetchKpiDetailByIdentifier(String identifier, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select mkd.id, name, description, data_type, is_custom, status, kpi_type_id, measure_units, cluster_operation," +
                    " created_time, updated_time, user_details_id, account_id, kpi_group_id, identifier, value_type, rollup_operation," +
                    " cluster_aggregation_type, instance_aggregation_type, is_informative, is_computed, cron_expression, delta_per_sec," +
                    " reset_delta_value from mst_kpi_details where identifier=%s";

            query = String.format(query, identifier);

            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(KpiDetail.class));
        } catch (EmptyResultDataAccessException e) {
            log.error("Domain to HEAL KPI mapping unavailable for Heal KPI Identifier {}", identifier);
            return null;
        } catch (Exception e) {
            log.error("Exception while fetching domain to HEAL KPI mapping for Heal KPI Identifier {}", identifier, e);
            return null;
        }
    }


    public boolean updateDomainEntityHealInstanceMapper(String domainInstanceName, String healAgent, String healInstance, String healService, String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "insert into domain_entity_heal_instance_mapper (domain_instance_name, heal_agent_uid, " +
                    "heal_instance_name,  heal_service_name, domain) values (?, ?, ?, ?, ?);";
            int value = jdbcTemplate.update(query, domainInstanceName, healAgent, healInstance, healService, domain);
            return value > 0;
        } catch (Exception e) {
            log.error("Error in update heal instances table domain_entity_heal_instance_mapper :  ", e);
        }
        return false;
    }

    public boolean addDomainEntities(String entityIdentifier, String entityType, String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "insert into domain_entities (entity_identifier, entity_type, domain) values (?, ?, ?);";
            int value = jdbcTemplate.update(query, entityIdentifier, entityType, domain);
            return value > 0;
        } catch (Exception e) {
            log.error("Error in insert to domain_entities table : {}", e.getMessage(), e);
        }
        return false;
    }

    public boolean deleteDomainEntityHealInstanceMapper(List<String> healInstanceNames, String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "delete from domain_entity_heal_instance_mapper where domain=? and heal_instance_name in "
                    + "(" + healInstanceNames.stream().map(id -> "?").collect(Collectors.joining(", ")) + ")";
            int value = jdbcTemplate.update(query, healInstanceNames, domain);
            return value > 0;
        } catch (Exception e) {
            log.error("Error in delete from table : {}", "domain_entity_heal_instance_mapper", e);
        }
        return false;
    }

    public void updateHealServicesInHealInstances(String identifier, String healServices, JdbcTemplate jdbcTemplate) {
        try {
            log.info("ID : {} , service id : {}", identifier, healServices);
            String query = String.format("UPDATE domain_entity_heal_instance_mapper SET heal_service_name = '%s' WHERE heal_instance_name = '%s'", healServices, identifier);
            jdbcTemplate.update(query);
        } catch (Exception e) {
            log.error("Error in updateMappingTable :  ", e);
        }
    }

    public List<KPITypeDetails> getKpiTypes(JdbcTemplate jdbcTemplate) {
        try {
            String query = "select type, typeid  typeId, name, subtypeid subTypeId from view_types where typeid=10";

            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KPITypeDetails.class));
        } catch (EmptyResultDataAccessException e) {
            log.error("KPI types are unavailable");
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Exception while fetching kpi types", e);
            return Collections.emptyList();
        }
    }

    public boolean deleteDomainEntities(List<String> entityIdentifiers, String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "delete from domain_entities where domain=? and entity_identifier in "
                    + "(" + entityIdentifiers.stream().map(id -> "?").collect(Collectors.joining(", ")) + ")";
            int value = jdbcTemplate.update(query, entityIdentifiers, domain);
            return value > 0;
        } catch (Exception e) {
            log.error("Error in delete from table : {}", "domain_entities", e);
        }
        return false;
    }

    public List<DomainEntity> fetchDomainEntities(String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select id, entity_identifier, entity_type, domain from domain_entities where domain = ?";

            return jdbcTemplate.query(query, (rs, rowNum) -> DomainEntity.builder()
                    .id(rs.getInt("id"))
                    .entityIdentifier(rs.getString("entity_identifier"))
                    .entityType(rs.getString("entity_type"))
                    .domain(rs.getString("domain"))
                    .build(), domain);
        } catch (Exception e) {
            log.error("Domain entity metric mapping is unavailable : {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public List<DomainHealInstance> fetchDomainEntityHealInstanceMapping(String domain, JdbcTemplate jdbcTemplate) {
        List<DomainHealInstance> serviceInstanceMappings = new ArrayList<>();
        try {
            String query = "SELECT heal_instance_name, heal_service_name, heal_agent_uid, domain_instance_name from domain_entity_heal_instance_mapper where domain = ?";

            serviceInstanceMappings = jdbcTemplate.query(query, (rs, rowNum) -> DomainHealInstance.builder()
                    .domainInstanceName(rs.getString("domain_instance_name"))
                    .healAgentUid(rs.getString("heal_agent_uid"))
                    .healInstanceName(rs.getString("heal_instance_name"))
                    .healServiceName(new HashSet<>(Arrays.asList(rs.getString("heal_service_name").split(","))))
                    .build(), domain);
        } catch (Exception e) {
            log.error("Error fetching domain_entity_heal_instance_mapper data: ", e);
        }
        return serviceInstanceMappings;
    }

    public List<DomainApplication> fetchDomainApplications(String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select id, application_id, application_name, application_identifier, api_key, node, domain " +
                    "from domain_application";
            if (domain != null) {
                query = String.format(query + " where domain='%s';", domain);
            }

            return jdbcTemplate.query(query, (rs, rowNum) -> DomainApplication.builder()
                    .id(rs.getInt("id"))
                    .applicationId(rs.getString("application_id"))
                    .applicationName(rs.getString("application_name"))
                    .applicationIdentifier(rs.getString("application_identifier"))
                    .apiKey(rs.getString("api_key"))
                    .node(rs.getString("node"))
                    .domain(rs.getString("domain"))
                    .build());
        } catch (Exception e) {
            log.error("Domain application is unavailable", e);
            return Collections.emptyList();
        }
    }

    public List<ConnectorTagMappingDetails> fetchConnectorTagMappingDetails(String tagName, String accountIdentifier, JdbcTemplate jdbcTemplate) {
        List<ConnectorTagMappingDetails> mappings = new ArrayList<>();
        try {
            String query = String.format("Select m.id, m.tag_id, m.object_id, m.object_ref_table, m.tag_key, m.tag_value, m.account_id FROM connector_tag_mapping AS m " +
                    "JOIN connector_tag_details AS d ON m.tag_id = d.id JOIN account AS a ON d.account_id = a.id WHERE d.name = '%s' AND a.identifier = '%s';", tagName, accountIdentifier);
            return jdbcTemplate.query(query, (rs, rowNum) -> ConnectorTagMappingDetails.builder()
                    .id(rs.getInt("id"))
                    .tagId(rs.getInt("tag_id"))
                    .objectId(rs.getInt("object_id"))
                    .objectRefTable(rs.getString("object_ref_table"))
                    .tagKey(rs.getString("tag_key"))
                    .tagValue(rs.getString("tag_value"))
                    .accountId(rs.getInt("account_id"))
                    .build());
        } catch (Exception hex) {
            log.error("Error fetching connectorTagMappingDetails: ", hex);
        }
        return mappings;
    }

    public List<DomainEntityTypeKpi> getHealKPIsForEntity(String domain, JdbcTemplate jdbcTemplate) {
        try {
            String query = "select mkd.id, mkd.name, mkd.identifier, kpi_group_id, mkg.name group_name, de.entity_identifier, de.entity_type, dm.metric_identifier, dhkm.aggregation_level " +
                    " from domain_to_heal_kpi_mappings dhkm join domain_metric dm join mst_kpi_details mkd join domain_entities de " +
                    " on de.entity_type = dm.entity_type and de.domain = dm.domain and de.domain = ? and dhkm.src_kpi_identifier = dm.metric_identifier " +
                    " and dhkm.heal_kpi_identifier =  mkd.identifier  left join  mst_kpi_group mkg on mkg.id = mkd.kpi_group_id";

            return jdbcTemplate.query(query, (rs, rowNum) -> DomainEntityTypeKpi.builder()
                    .entityIdentifier(rs.getString("entity_identifier"))
                    .mstKpiId(rs.getInt("id"))
                    .mstKpiName(rs.getString("name"))
                    .mstKpiIdentifier(rs.getString("identifier"))
                    .isGroupKpi(rs.getBoolean("kpi_group_id"))
                    .mstGroupName(rs.getString("group_name"))
                    .entityType(rs.getString("entity_type"))
                    .srcMetricIdentifier(rs.getString("metric_identifier"))
                    .aggregationLevel(rs.getString("aggregation_level"))
                    .build(), domain);
        } catch (EmptyResultDataAccessException e) {
            log.error("Domain entity to HEAL KPI mapping unavailable for domain:{}", domain);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Exception while fetching domain entity to HEAL KPI mapping for connector:{}", domain, e);
            return Collections.emptyList();
        }
    }
}
