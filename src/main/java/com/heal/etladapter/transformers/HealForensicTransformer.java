package com.heal.etladapter.transformers;

import com.appnomic.appsone.common.protbuf.A1EventProtos;
import com.heal.etladapter.beans.*;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.DateHelper;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HealForensicTransformer extends AbstractTransformer<List<DomainKpi>, List<A1EventProtos.A1Event>> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private ConnectorKpiMaster connectorKpiMaster;

    private Map<Integer, String> kpiTypes = new HashMap<>();
    private final Map<String, KpiDetail> healKpis = new HashMap<>();
    private Map<String, DomainHealInstance> instanceMap = new HashMap<>();
    private Map<String, DomainToHealKpiMappings> kpiMappings = new HashMap<>();
    private Properties errorProperty;

    @Override
    public void initialize() throws Exception {
        long st = System.currentTimeMillis();

        log.info("Initialization for transformer {} - BEGIN", this.className);

        try {
            log.info("Reloading configuration from ConnectorKpiMaster for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);

            if (this.parameters.containsKey(AdapterConstants.DOMAIN) && this.parameters.get(AdapterConstants.DOMAIN).equalsIgnoreCase("Prometheus")) {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .filter(o -> o.getHealAgentUid().endsWith("_kpi") || o.getDomainInstanceName().equalsIgnoreCase("cluster"))
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            } else {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            }

            List<KpiDetail> healKpiList = connectorKpiMaster.getHealKPIs(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
            healKpis.clear();
            for (KpiDetail kpi : healKpiList) {
                healKpis.put(kpi.getIdentifier(), kpi);
            }

            kpiMappings = connectorKpiMaster.getDomainToHealKpiMappings(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                    .collect(Collectors.toMap(DomainToHealKpiMappings::getSrcKpiIdentifier, p -> p, (o, n) -> n));

            kpiTypes = connectorKpiMaster.getKpiTypes(this.jdbcTemplate).stream().collect(Collectors.toMap(KPITypeDetails::getSubTypeId, KPITypeDetails::getName));

            log.info("Instance map size:{}, heal kpis size:{}, kpi mapping size:{}, appsoneKpiTypes: {}, jobId:{}, connector instance:{}", instanceMap.size(), healKpis.size(), kpiMappings.size(), kpiTypes.size(), jobId, connectorInstanceIdentifier);

            FileReader errorFileReader;

            if (!this.parameters.getOrDefault(AdapterConstants.ERROR_PROPERTY, "").trim().isEmpty()) {
                errorFileReader = new FileReader(this.parameters.get(AdapterConstants.ERROR_PROPERTY));
                errorProperty = new Properties();
                errorProperty.load(errorFileReader);
            }
        } catch (Exception e) {
            log.error("Error in initializing transformer {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier, e);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_INITIALIZATION_ERROR, 1);
            throw new EtlAdapterException(e.getMessage() + " for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (healKpis.isEmpty()) {
            log.error("Heal metrics unavailable to process metrics. Failing the transformer initialization for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_CONFIGURATION_ERROR, 1);
            throw new EtlAdapterException("Heal metrics details unavailable for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (instanceMap.isEmpty()) {
            log.error("Heal instances mapping is unavailable. Failing the transformer initialization for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_CONFIGURATION_ERROR, 1);
            throw new EtlAdapterException("Heal instances mapping unavailable for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        log.info("Initialization for transformer {} for jobId:{}, connector instance:{} - END. Time taken: {} ms", this.className, jobId, connectorInstanceIdentifier, (System.currentTimeMillis() - st));
    }

    @Override
    public List<A1EventProtos.A1Event> transform(List<DomainKpi> items) {
        if (items == null || items.isEmpty()) {
            log.warn("Forensic Transformer not received any data from extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }

        healthMetrics.putInTransformerReceivedCount(this.className, items.size());

        List<A1EventProtos.A1Event> a1Events = new ArrayList<>();
        try {

            if (this.parameters.containsKey(AdapterConstants.DOMAIN) && this.parameters.get(AdapterConstants.DOMAIN).equalsIgnoreCase("Prometheus")) {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .filter(o -> o.getHealAgentUid().endsWith("_kpi") || o.getDomainInstanceName().equalsIgnoreCase("cluster"))
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            } else {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            }

            items.forEach(src -> {
                try {
                    try {
                        src.setCollectionInterval((int) (src.getUpperThreshold().getTime() - src.getLowerThreshold().getTime()) / 1000);
                    } catch (Exception e) {
                        log.error("Exception while setting collection for src : {} , jobId:{}, connector instance:{} ", src, jobId, connectorInstanceIdentifier, e);
                    }

                    log.debug("Transforming Domain KPI {} for jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier);

                    KpiDetail kpiDetail = null;
                    DomainToHealKpiMappings kpi = kpiMappings.get(src.getKpiName());
                    if (kpi != null) {
                        kpiDetail = healKpis.get(kpi.getHealKpiIdentifier());
                    }

                    if (null == kpiDetail) {
                        log.error("HEAL KPI unavailable for Domain KPI {}. Discarding the KPI for jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier);
                        healthMetrics.putInTransformerDropCount(this.className, 1);
                        return;
                    }

                    if (!this.instanceMap.containsKey(src.getDomainInstanceId())) {
                        log.warn("HEAL instance unavailable for domain instance: {}. Falling back to default instance identifier, instance map size:{}, jobId:{}, connector instance:{}", src.getDomainInstanceId(), this.instanceMap.size(), jobId, connectorInstanceIdentifier);
                    }

                    log.trace("Begin Transforming A1Event data for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                    A1EventProtos.A1Event a1Event = getA1Event(src, kpiDetail);

                    if (a1Event != null) {
                        a1Events.add(a1Event);
                    }
                } catch (Exception e) {
                    log.error("Error occurred while for processing the adapter item:{}, jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier, e);
                    healthMetrics.putInTransformerDropCount(this.className, 1);
                }
            });

        } catch (Exception e) {
            log.error("Error occurred while processing Forensic transformer for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }

        log.info("Transformation complete for domain KPIs. A1Event count: {}, jobId:{}, connector instance:{}", a1Events.size(), jobId, connectorInstanceIdentifier);

        return a1Events;
    }

    public A1EventProtos.A1Event getA1Event(DomainKpi src, KpiDetail healKpi) {
        try {
            String instanceId = this.instanceMap.containsKey(src.getDomainInstanceId())
                    ? this.instanceMap.get(src.getDomainInstanceId()).getHealInstanceName()
                    : this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_INSTANCE, src.getDomainInstanceId());

            if (instanceId == null) {
                log.error("InstanceID is null in the adapterItem source: {}. Hence, skipping the instance data for jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier);
                return null;
            }

            String agentUId = StringUtil.EMPTY_STRING;

            if (this.instanceMap.containsKey(src.getDomainInstanceId())) {
                agentUId = instanceMap.get(src.getDomainInstanceId()).getHealAgentUid();
            }
            if (agentUId.equals(StringUtil.EMPTY_STRING)) {
                log.warn("Instance to agent mapping unavailable for instance: {}. Falling back to default agent identifier. Instance map size:{}, jobId:{}, connector instance:{}", src.getDomainInstanceId(), instanceMap.size(), jobId, connectorInstanceIdentifier);
                agentUId = this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_AGENT, src.getDomainInstanceId());
            }

            A1EventProtos.A1Event.Builder a1EventBuilder = A1EventProtos.A1Event.newBuilder()
                    .setAgentUid(agentUId)
                    .setInstanceId(instanceId)
                    .setKpiId(healKpi.getId())
                    .setKpiName(healKpi.getName())
                    .setTimeInGMT(DateHelper.date2GMTString(src.getUpperThreshold()));

            if (src.getErrorPattern() != null) {
                a1EventBuilder.setErrorCode(src.getErrorPattern() + ":" + errorProperty.getProperty(src.getErrorPattern()));
                a1EventBuilder.setValue("null");
            } else {
                a1EventBuilder.setValue(src.getValue() != null ? src.getValue().toString() : "null");
            }

            return a1EventBuilder.build();
        } catch (Exception e) {
            log.error("Error creating A1Event for src: {}, jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier, e);
            return null;
        }
    }
}
