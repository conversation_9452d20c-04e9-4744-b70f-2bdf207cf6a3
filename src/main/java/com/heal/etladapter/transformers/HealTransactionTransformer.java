package com.heal.etladapter.transformers;

import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos.PSAgentMessage;
import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos.ResponseTime;
import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos.ResponseTime.ResponseStatusTag;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.repo.redis.ServiceRepo;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.DateHelper;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;


@Slf4j
@Component
public class HealTransactionTransformer extends AbstractTransformer<List<Map<String, String>>, List<PSAgentMessage>> {

    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private ConnectorKpiMaster connectorKpiMaster;
    @Autowired
    private ServiceRepo serviceRepo;

    @Override
    public void initialize() throws EtlAdapterException {

    }

    @Override
    public List<PSAgentMessage> transform(List<Map<String, String>> items) {
        List<PSAgentMessage> psAgentMessages = new ArrayList<>();

        if (items == null || items.isEmpty()) {
            log.warn("Transaction Transformer not received any data from extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }

        healthMetrics.putInTransformerReceivedCount(this.className, items.size());


        items.forEach(rawData -> {
            if (!rawData.containsKey(AdapterConstants.HTTP_URI)) {
                log.error("URI field not present in Document: {}. Hence, dropping Item for jobId:{}, connector instance:{}", rawData, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }
            if (!rawData.containsKey((AdapterConstants.HTTP_RESPONSE_TIME))) {
                log.error("Response Time field not present in Document: {}. Hence, dropping Item for jobId:{}, connector instance:{}", rawData, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }

            double responseTime;
            try {
                responseTime = Double.parseDouble(rawData.get(AdapterConstants.HTTP_RESPONSE_TIME));
            } catch (NumberFormatException ex) {
                log.info("Getting error when converting response time. Hence, dropping item: {} for jobId:{}, connector instance:{}", rawData, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }

            String instanceName = rawData.get(AdapterConstants.INSTANCE_NAME);
            String agentUid = StringUtil.EMPTY_STRING;
            List<BasicAgentEntity> agentsForService = serviceRepo.getAgentsForService(this.parameters.get(AdapterConstants.ACCOUNT_NAME), instanceName);
            if (agentsForService != null && !agentsForService.isEmpty()) {
                Optional<BasicAgentEntity> agentEntity = agentsForService.stream().filter(ba -> AdapterConstants.LOG_AGENT.equalsIgnoreCase(ba.getType())).findFirst();
                if (agentEntity.isPresent()) {
                    BasicAgentEntity basicAgentEntity = agentEntity.get();
                    agentUid = basicAgentEntity.getIdentifier();
                } else {
                    log.info("Instance to agent mapping unavailable for instance : {}, jobId:{}, connector instance:{}", instanceName, jobId, connectorInstanceIdentifier);
                }
            }
            if (agentUid.equals(StringUtil.EMPTY_STRING)) {
                agentUid = this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_AGENT, instanceName);
            }

            // Uncomment only if testing with backdated data. NOT TO BE USED IN PROD.
            //Date endTimeInGMT = DateHelper.string2ElkDateConverter(rawData.get(Constants.LOGSCAN_SCHEMA_TIMESTAMP));
            Date endTimeInGMT = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(endTimeInGMT);
            cal.add(Calendar.MILLISECOND, -1 * ((int) responseTime));
            Date startTimeInGMT = cal.getTime();

            int statusCode = -1;
            String httpMethod = rawData.getOrDefault(AdapterConstants.HTTP_REQUEST_METHOD, "GET");

            ResponseTime.Builder responseTimeBuilder = ResponseTime
                    .newBuilder().setEndTimeInGMT(DateHelper.date2GMTString4Txn(endTimeInGMT))
                    .setStartTimeInGMT(DateHelper.date2GMTString4Txn(startTimeInGMT))
                    .setResponseInMicroseconds((int) responseTime)
                    .setResponseTimeType(ResponseTime.ResponseTimeType.DC);

            if (rawData.containsKey(AdapterConstants.HTTP_RESPONSE_STATUS_CODE)) {
                try {
                    statusCode = Integer.parseInt(rawData.get(AdapterConstants.HTTP_RESPONSE_STATUS_CODE));
                    if (statusCode >= 400) {
                        responseTimeBuilder.setResponseStatusTag(ResponseStatusTag.FAIL);
                    } else {
                        responseTimeBuilder.setResponseStatusTag(ResponseStatusTag.GOOD);
                    }
                } catch (NumberFormatException ex) {
                    log.error("Exception in parsing response status {}. Defaulting to {}. rawData: {}, jobId:{}, connector instance:{}", rawData.get(AdapterConstants.HTTP_RESPONSE_STATUS_CODE), ResponseStatusTag.UNKNOWN, rawData, jobId, connectorInstanceIdentifier);
                    responseTimeBuilder.setResponseStatusTag(ResponseStatusTag.UNKNOWN);

                }
            } else {
                log.warn("Response status unavailable. Defaulting to {}, rawData: {}, jobId:{}, connector instance:{}", ResponseStatusTag.UNKNOWN, rawData, jobId, connectorInstanceIdentifier);
                responseTimeBuilder.setResponseStatusTag(ResponseStatusTag.UNKNOWN);
            }

            PSAgentMessageProtos.Http.Builder httpBuilder = PSAgentMessageProtos.Http.newBuilder()
                    .setUrl(rawData.get(AdapterConstants.HTTP_URI)).setResponseCode(statusCode);

            try {
                httpBuilder.setMethod(PSAgentMessageProtos.Http.Method.valueOf(httpMethod.toUpperCase()));
            } catch (IllegalArgumentException e) {
                log.error("Exception in processing http method {}. Defaulting to GET. rawData {}, jobId:{}, connector instance:{}", httpMethod.toUpperCase(), rawData, jobId, connectorInstanceIdentifier, e);
                httpBuilder.setMethod(PSAgentMessageProtos.Http.Method.GET);
            }

            ResponseTime respTime = responseTimeBuilder.build();

            PSAgentMessage.Builder msgBuilder = PSAgentMessage.newBuilder().addResponseTime(respTime)
                    .setAgentUid(agentUid).setTransactionType(PSAgentMessage.TransactionType.HTTP)
                    .setHttp(httpBuilder.build());

            if (rawData.containsKey(AdapterConstants.CLIENT_IP)) {
                msgBuilder.setClientIp(rawData.get(AdapterConstants.CLIENT_IP));
            }
            if (rawData.containsKey(AdapterConstants.SERVER_IP)) {
                msgBuilder.setServerIp(rawData.get(AdapterConstants.SERVER_IP));
            }
            if (rawData.containsKey(AdapterConstants.SERVER_PORT)) {
                msgBuilder.setServerPort(Integer.parseInt(rawData.get(AdapterConstants.SERVER_PORT)));
            }

            PSAgentMessage msg = msgBuilder.build();

            log.debug("PSAgentMessage : {}, jobId:{}, connector instance:{}", msg, jobId, connectorInstanceIdentifier);
            psAgentMessages.add(msg);
        });

        healthMetrics.putInTransformerProcessedCount(this.className, psAgentMessages.size());

        log.info("Transformed transactions for jobId:{}, connector instance:{}, count: {}", jobId, connectorInstanceIdentifier, psAgentMessages.size());

        return psAgentMessages;
    }
}
