package com.heal.etladapter.transformers;

import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.Service;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.beans.ConnectorTagMappingDetails;
import com.heal.etladapter.beans.DomainEntity;
import com.heal.etladapter.beans.DomainHealInstance;
import com.heal.etladapter.beans.cc.*;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.TopologyDetails;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.repo.redis.AgentRepo;
import com.heal.etladapter.repo.redis.InstanceRepo;
import com.heal.etladapter.repo.redis.ServiceRepo;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.CCPayloadUtility;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class HealTopologyTransformer extends AbstractTransformer<List<TopologyDetails>, List<CCPayload>> {
    @Autowired
    protected ConnectorKpiMaster connectorKpiMaster;
    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private ServiceRepo serviceRepo;
    @Autowired
    private InstanceRepo instanceRepo;
    @Autowired
    private AgentRepo agentRepo;
    @Autowired
    private CCPayloadUtility ccPayloadUtility;

    private List<DomainHealInstance> domainHealInstanceList = new ArrayList<>();
    private Map<String, String> dtCompNameMapping = new HashMap<>(); // mapping for component name and version
    private Map<String, String> domainHealLayerMapping = new HashMap<>();
    Map<String, List<DomainEntity>> domainEntities = new HashMap<>();

    @Override
    public void initialize() throws EtlAdapterException {
        try {
            reloadMapping();

        } catch (Exception e) {
            log.error("Getting the error when initializing the Chainable worker for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
            healthMetrics.putInExtractorErrors(Constants.TOPOLOGY_TRANSFORMER_INITIALIZE_ERROR, 1);

            throw new EtlAdapterException(e.getMessage());
        }
        if (this.parameters.get(Constants.TOPOLOGY_APPLICATION_IDENTIFIER) == null || this.parameters.get(Constants.TOPOLOGY_APPLICATION_IDENTIFIER).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(Constants.TOPOLOGY_TRANSFORMER_CONFIGURATION_ERROR, 1);
            log.error("'topology.application.identifier' configuration parameter unavailable or invalid for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            throw new EtlAdapterException("'topology.application.identifier' configuration parameter unavailable or invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (this.parameters.get(AdapterConstants.ACCOUNT_NAME) == null || this.parameters.get(AdapterConstants.ACCOUNT_NAME).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(Constants.TOPOLOGY_TRANSFORMER_CONFIGURATION_ERROR, 1);
            log.error("'account.name' configuration parameter unavailable or invalid for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            throw new EtlAdapterException("'account.name' configuration parameter unavailable or invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
    }

    private void reloadMapping() {
        log.trace("Inside reloadMapping() method.");
        try {
            // currently we are getting appId, serviceLayer and timezone info from prerequisite table
            domainHealInstanceList = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
            dtCompNameMapping = connectorKpiMaster.fetchConnectorTagMappingDetails(this.parameters.getOrDefault(AdapterConstants.DT_COMPONENT_TAG_MAPPING_NAME, "DtComponentMapper"),
                    this.parameters.get(AdapterConstants.ACCOUNT_NAME), this.jdbcTemplate).stream().collect(Collectors.toMap(ConnectorTagMappingDetails::getTagKey, ConnectorTagMappingDetails::getTagValue, (r, n) -> r));
            domainHealLayerMapping = connectorKpiMaster.fetchConnectorTagMappingDetails(this.parameters.getOrDefault(AdapterConstants.DT_SERVICE_LAYER_TAG_MAPPING_NAME, "DtServiceLayer"),
                    this.parameters.get(AdapterConstants.ACCOUNT_NAME), this.jdbcTemplate).stream().collect(Collectors.toMap(ConnectorTagMappingDetails::getTagKey, ConnectorTagMappingDetails::getTagValue, (r, v) -> r));
            domainEntities = connectorKpiMaster.fetchDomainEntities(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate)
                    .stream().collect(Collectors.groupingBy(DomainEntity::getEntityIdentifier));
        } catch (Exception e) {
            healthMetrics.putInExtractorErrors(Constants.TOPOLOGY_TRANSFORMER_ERRORS, 1);
            log.error("Error occurred in reload mapping function for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }
    }

    private Map<String, Set<String>> getRedisServiceConnectionMapping() {
        log.info("Inside getServiceConnectionMapping() method for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);

        Map<String, Set<String>> resultMapping = new HashMap<>();
        try {
            Map<Integer, List<BasicEntity>> inbounds = serviceRepo.getInboundDetails(this.parameters.get(AdapterConstants.ACCOUNT_NAME));
            Map<Integer, List<BasicEntity>> outbounds = serviceRepo.getOutboundDetails(this.parameters.get(AdapterConstants.ACCOUNT_NAME));
            Map<Integer, String> sourceServicesMapping = inbounds.values().stream().flatMap(Collection::stream).collect(Collectors.toMap(BasicEntity::getId, BasicEntity::getIdentifier, (r, v) -> r));
            outbounds.keySet().forEach(id -> {
                List<BasicEntity> basicEntities = outbounds.get(id);
                Set<String> destServices = resultMapping.containsKey(sourceServicesMapping.get(id)) ? resultMapping.get(sourceServicesMapping.get(id)) : new HashSet<>();
                basicEntities.forEach(entity -> destServices.add(entity.getIdentifier()));
                resultMapping.put(sourceServicesMapping.get(id), destServices);
            });
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_INITIALIZATION_ERROR, 1);
            log.error("Error occurred in fetching service connections from redis for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }
        log.debug("Service connection map : {}, jobId:{}, connector instance:{}", resultMapping, jobId, connectorInstanceIdentifier);
        return resultMapping;
    }

    @Override
    public List<CCPayload> transform(List<TopologyDetails> topologyDetailsList) {
        if (topologyDetailsList == null || topologyDetailsList.isEmpty()) {
            log.info("Extractor sent empty results to topology transformer for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            healthMetrics.putInTransformerErrors(Constants.TOPOLOGY_TRANSFORMER_ERRORS, 1);
            return null;
        }

        List<CCPayload> ccPayloads = new ArrayList<>();
        String applicationIdentifier = this.parameters.get(Constants.TOPOLOGY_APPLICATION_IDENTIFIER);
        String timeZone = this.parameters.getOrDefault(Constants.TOPOLOGY_TIME_ZONE, "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi");

        log.info("Number of entities received from extractor is {} for topology transformer, jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, topologyDetailsList.size());

        healthMetrics.putInTransformerReceivedCount(this.className, topologyDetailsList.size());

        Set<String> instanceIdentifiers = new HashSet<>();
        Map<String, CCPayload> hostVsCreationPayload = new HashMap<>();
        Map<String, CCPayload> hostVsUpdatePayload = new HashMap<>();
        Map<String, CCPayload> agentVsCreationPayload = new HashMap<>();

        topologyDetailsList.forEach(topologyDetails -> {
            TopologyDetails.Service service = topologyDetails.getService();

            if (domainHealInstanceList.stream().noneMatch(d -> d.getHealServiceName().contains(service.getIdentifier()))) {
                log.warn("Service {} is not mapped to any instances in connector. Will be mapped to the concerned hosts and component instances for jobId:{}, connector instance:{}", service.getIdentifier(), jobId, connectorInstanceIdentifier);
            }

            Service existingService = serviceRepo.getServiceDetails(this.parameters.get(AdapterConstants.ACCOUNT_NAME), service.getIdentifier());
            if (existingService != null) {
                log.info("Service:{} already exists. Service will not be added to HEAL for jobId:{}, connector instance:{}", service.getIdentifier(), jobId, connectorInstanceIdentifier);
            } else {
                log.warn("Service:{} is unavailable in HEAL but present at connector. Same will be created in HEAL for jobId:{}, connector instance:{}", service.getIdentifier(), jobId, connectorInstanceIdentifier);
                String layer = domainHealLayerMapping.getOrDefault(service.getLayer(), "DEFAULT");
                HealServicePayload servicePayload = ccPayloadUtility.getHealServicePayload(service.getIdentifier(), service.getIdentifier(), layer, applicationIdentifier, timeZone);

                log.debug("Service {} will be created. CC payload: {}, jobId:{}, connector instance:{}", service.getIdentifier(), servicePayload, jobId, connectorInstanceIdentifier);

                CCPayload ccServicePayload = CCPayload.builder().healPayload(servicePayload).entityType(Constants.SERVICE).build();
                ccPayloads.add(ccServicePayload);
            }

            Map<String, Set<TopologyDetails.Instance>> hostVsCompInstances = topologyDetails.getHostWiseCompInstances();
            Set<TopologyDetails.Instance> hosts = topologyDetails.getHostInstances();

            hosts.forEach(host -> {
                String caAgentIdentifier = host.getIdentifier().concat("_").concat(AdapterConstants.COMP_AGENT);
                String lfAgentIdentifier = host.getIdentifier().concat("_").concat(AdapterConstants.LOG_AGENT);

                String ipAddress = host.getIpAddress();

                try {
                    ccPayloads.addAll(getAgentCreationPayload(host, service, agentVsCreationPayload));
                    String compName = host.getOsVersion() != null && dtCompNameMapping.containsKey(host.getOsVersion()) ? dtCompNameMapping.get(host.getOsVersion()) : Constants.DEFAULT_HOST_COMPONENT;
                    String compVer = host.getOsVersion() != null && dtCompNameMapping.containsKey(host.getOsVersion()) ? dtCompNameMapping.get(host.getOsVersion()) : Constants.DEFAULT_COMPONENT_VERSION;

                    CompInstClusterDetails existingHostInstance = instanceRepo.getInstanceDetails(this.parameters.get(AdapterConstants.ACCOUNT_NAME), host.getIdentifier());
                    if (existingHostInstance == null) {
                        log.info("Host:{} will be added and mapped to service:{}, jobId:{}, connector instance:{}", host.getIdentifier(), service.getIdentifier(), jobId, connectorInstanceIdentifier);

                        if (hostVsCreationPayload.containsKey(host.getIdentifier())) {
                            CCPayload ccPayload = hostVsCreationPayload.get(host.getIdentifier());
                            HealInstancePayload payload = (HealInstancePayload) ccPayload.getHealPayload();
                            Set<String> serviceIds = new HashSet<>(payload.getServiceIdentifiers());
                            serviceIds.add(service.getIdentifier());
                            payload.setServiceIdentifiers(serviceIds);
                            hostVsCreationPayload.put(host.getIdentifier(), ccPayload);
                        } else {

                            HealInstancePayload hostPayload = ccPayloadUtility.getHostInstancePayload(ipAddress, host.getIdentifier(), host.getIdentifier(),
                                    List.of(caAgentIdentifier, lfAgentIdentifier), Set.of(service.getIdentifier()), compName, compVer,
                                    this.parameters.getOrDefault(AdapterConstants.ENVIRONMENT_NAME, "NONE"));

                            log.debug("Host instance id: {}, payload : {}, jobId:{}, connector instance:{}", host.getIdentifier(), hostPayload, jobId, connectorInstanceIdentifier);

                            //HealInstancePayload hostPayload = getHealInstancePayload(host, caAgentIdentifier, lfAgentIdentifier, service, ipAddress);
                            if (hostPayload != null) {
                                CCPayload ccPayload = CCPayload.builder().healPayload(hostPayload).entityType(AdapterConstants.HOST_INSTANCE).build();
                                hostVsCreationPayload.put(host.getIdentifier(), ccPayload);
                            }
                        }
                    } else {
                        List<BasicEntity> services = instanceRepo.getInstanceWiseServices(this.parameters.get(AdapterConstants.ACCOUNT_NAME), host.getIdentifier());
                        if (services.stream().anyMatch(s -> s.getIdentifier().equalsIgnoreCase(service.getIdentifier()))) {
                            log.info("Host:{} already exists and is mapped to service:{}, jobId:{}, connector instance:{}", host.getIdentifier(), service.getIdentifier(), jobId, connectorInstanceIdentifier);
                        } else {
                            log.warn("Service:{} is not mapped to host:{} in HEAL but mapping is present at connector. Same will be updated in HEAL for jobId:{}, connector instance:{}", service.getIdentifier(), host.getIdentifier(), jobId, connectorInstanceIdentifier);

                            HealInstanceUpdatePayload healInstanceUpdatePayload = updateServiceInstanceMappingInHeal(host, service, existingHostInstance);

                            if (hostVsUpdatePayload.containsKey(host.getIdentifier())) {
                                CCPayload ccPayload = hostVsUpdatePayload.get(host.getIdentifier());
                                HealInstanceUpdatePayload payload = (HealInstanceUpdatePayload) ccPayload.getHealPayload();

                                if (healInstanceUpdatePayload != null) {
                                    Set<String> newApplications = healInstanceUpdatePayload.getApplication().stream().map(ApplicationPojo::getIdentifier).collect(Collectors.toSet());
                                    ApplicationPojo applicationPojo = payload.getApplication().stream()
                                            .filter(p -> newApplications.contains(p.getIdentifier()))
                                            .findFirst().orElse(null);

                                    if (applicationPojo != null) {
                                        List<ServicePojo> mutableServiceList = new ArrayList<>(applicationPojo.getService());
                                        mutableServiceList.addAll(healInstanceUpdatePayload.getApplication().get(0).getService());
                                        applicationPojo.setService(mutableServiceList);
                                    } else {
                                        payload.getApplication().addAll(healInstanceUpdatePayload.getApplication());
                                    }
                                    hostVsUpdatePayload.put(host.getIdentifier(), ccPayload);
                                }
                            } else {
                                if (healInstanceUpdatePayload != null) {
                                    CCPayload hostInstanceUpdatePayload = CCPayload.builder().healPayload(healInstanceUpdatePayload).entityType(AdapterConstants.HOST_INSTANCE_UPDATE).build();
                                    hostVsUpdatePayload.put(host.getIdentifier(), hostInstanceUpdatePayload);
                                }
                            }
                        }
                        //This is to take care of any missing entries on connector side
                        updateDomainEntitiesTable(host, AdapterConstants.HOST_INSTANCE);
                    }
                } catch (Exception e) {
                    log.error("Exception in Host payload creation, msg : {}, jobId:{}, connector instance:{}", e.getMessage(), jobId, connectorInstanceIdentifier, e);
                }

                reloadMapping();

                Set<TopologyDetails.Instance> compInstances = hostVsCompInstances.getOrDefault(host.getIdentifier(), new HashSet<>());

                compInstances.stream()
                        .filter(c -> !instanceIdentifiers.contains(c.getIdentifier()))
                        .forEach(compInstance -> {
                            instanceIdentifiers.add(compInstance.getIdentifier());
                            String compName = compInstance.getOsVersion() != null && dtCompNameMapping.containsKey(compInstance.getOsVersion()) ? dtCompNameMapping.get(compInstance.getOsVersion()) : Constants.DEFAULT_INSTANCE_COMPONENT;
                            String compVer = compInstance.getOsVersion() != null && dtCompNameMapping.containsKey(compInstance.getOsVersion()) ? dtCompNameMapping.get(compInstance.getOsVersion()) : Constants.DEFAULT_COMPONENT_VERSION;

                            CompInstClusterDetails existingCompInstance = instanceRepo.getInstanceDetails(this.parameters.get(AdapterConstants.ACCOUNT_NAME), compInstance.getIdentifier());
                            if (existingCompInstance == null) {
                                log.info("CompInstance {} will be added and mapped to service:{}, jobId:{}, connector instance:{}", compInstance.getIdentifier(), service.getIdentifier(), jobId, connectorInstanceIdentifier);
                                String instanceIpAddress = compInstance.getIpAddress() != null ? compInstance.getIpAddress() : ipAddress;
                                HealInstancePayload hostPayload = ccPayloadUtility.getCompInstancePayload(instanceIpAddress, compInstance.getIdentifier(), compInstance.getIdentifier(),
                                        List.of(caAgentIdentifier, lfAgentIdentifier), Set.of(service.getIdentifier()), compName, compVer,
                                        this.parameters.getOrDefault(AdapterConstants.ENVIRONMENT_NAME, "NONE"));

                                log.debug("Comp instance id: {}, payload : {}, jobId:{}, connector instance:{}", compInstance.getIdentifier(), hostPayload, jobId, connectorInstanceIdentifier);
                                if (hostPayload != null) {
                                    CCPayload ccPayload = CCPayload.builder().healPayload(hostPayload).entityType(AdapterConstants.COMP_INSTANCE).build();
                                    ccPayloads.add(ccPayload);
                                }
                            } else {
                                //This is to take care of any missing entries on connector side
                                updateDomainEntitiesTable(compInstance, AdapterConstants.COMP_INSTANCE);
                                List<BasicEntity> services = instanceRepo.getInstanceWiseServices(this.parameters.get(AdapterConstants.ACCOUNT_NAME), compInstance.getIdentifier());
                                if (services.stream().anyMatch(s -> s.getIdentifier().equalsIgnoreCase(service.getIdentifier()))) {
                                    log.info("CompInstance {} already exists and is mapped to service:{}, jobId:{}, connector instance:{}", compInstance.getIdentifier(), service.getIdentifier(), jobId, connectorInstanceIdentifier);
                                } else {
                                    log.warn("compInstance {} cannot be mapped to service {} as it is already mapped to service {}, jobId:{}, connector instance:{}", compInstance.getIdentifier(), service.getIdentifier(), services, jobId, connectorInstanceIdentifier);
                                }
                            }
                        });
            });
        });

        ccPayloads.addAll(hostVsUpdatePayload.values());
        ccPayloads.addAll(hostVsCreationPayload.values());

        Map<String, Set<String>> serviceConnectionEntities = getServiceConnectionEntities(topologyDetailsList);
        Map<String, Set<String>> serviceConnectionMapping = getRedisServiceConnectionMapping();
        Set<HealServiceConnectionPayload> serviceConnPayload = new HashSet<>();
        // service connection payload creation
        serviceConnectionEntities.keySet().forEach(srcServiceId -> serviceConnectionEntities.get(srcServiceId).forEach(destServiceId -> {
            if (srcServiceId.equalsIgnoreCase(destServiceId)) {
                log.error("Source and destination service identifiers are same with value {}. Ignoring the connection request for jobId:{}, connector instance:{}", srcServiceId, jobId, connectorInstanceIdentifier);
                return;
            }

            if (serviceConnectionMapping.containsKey(srcServiceId) && serviceConnectionMapping.get(srcServiceId).contains(destServiceId)) {
                log.info("Service connection already exists! srcId : {} and destId : {}, jobId:{}, connector instance:{}", srcServiceId, destServiceId, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }
            HealServiceConnectionPayload healServiceConnectionPayload = ccPayloadUtility.getHealServiceConnectionPayload(srcServiceId, destServiceId);

            log.debug("Service connection {} will be created. CC payload: {}, jobId:{}, connector instance:{}", srcServiceId, healServiceConnectionPayload, jobId, connectorInstanceIdentifier);
            serviceConnPayload.add(healServiceConnectionPayload);
        }));

        List<CCPayload> svcPaylods = serviceConnPayload.stream().map(scp -> CCPayload.builder()
                .healPayload(scp)
                .entityType(AdapterConstants.SERVICE_CONNECTION)
                .build()).toList();
        ccPayloads.addAll(svcPaylods);

        // check in-active component instances
        if (Boolean.parseBoolean(this.parameters.getOrDefault(Constants.REMOVE_INSTANCES, "false"))) {
            Set<String> allInstances = domainHealInstanceList.stream().map(DomainHealInstance::getDomainInstanceName).collect(Collectors.toSet());
            allInstances.removeAll(instanceIdentifiers);
            if (allInstances.isEmpty()) {
                log.info("No inactive/old component instances for deletion! for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            } else {
                // create delete payload
                allInstances.forEach(delInstance -> {
                    HealInstanceDeletePayload healInstanceDeletePayload = ccPayloadUtility.getHealInstanceDeletePayload(delInstance, AdapterConstants.COMP_INSTANCE);
                    ccPayloads.add(CCPayload.builder().healPayload(healInstanceDeletePayload).entityType(AdapterConstants.DELETE_INSTANCE).build());

                    log.debug("Component instance {} will be deleted. CC payload: {}, jobId:{}, connector instance:{}", delInstance, healInstanceDeletePayload, jobId, connectorInstanceIdentifier);
                });
            }
        }

        log.trace("Entities identified at transformer are {} for jobId:{}, connector instance:{}", ccPayloads, jobId, connectorInstanceIdentifier);
        log.info("Transformation completed for topology entities count: {} for jobId:{}, connector instance:{}", ccPayloads.size(), jobId, connectorInstanceIdentifier);

        healthMetrics.putInTransformerProcessedCount(this.className, ccPayloads.size());

        return ccPayloads;
    }

    private List<CCPayload> getAgentCreationPayload(TopologyDetails.Instance host, TopologyDetails.Service service, Map<String, CCPayload> agentVsCreationPayload) {
        String ipAddress = host.getIpAddress();
        String caAgentIdentifier = host.getIdentifier().concat("_").concat(AdapterConstants.COMP_AGENT);
        String lfAgentIdentifier = host.getIdentifier().concat("_").concat(AdapterConstants.LOG_AGENT);

        List<CCPayload> ccPayloads = new ArrayList<>();

        List<String> agentIdentifiers = agentRepo.getAccountWiseAgents(this.parameters.get(AdapterConstants.ACCOUNT_NAME)).stream().map(BasicEntity::getIdentifier).toList();
        if (!agentVsCreationPayload.containsKey(caAgentIdentifier) && !agentIdentifiers.contains(caAgentIdentifier)) {
            HealAgentPayload componentAgentPayload = ccPayloadUtility.getHealAgentPayload(caAgentIdentifier, service.getIdentifier(), ipAddress, AdapterConstants.COMP_AGENT,
                    this.parameters.getOrDefault(AdapterConstants.HEAL_GRPCENDPT_ADDR, ""), this.parameters.getOrDefault(AdapterConstants.HEAL_GRPCENDPT_PORT, ""));

            CCPayload caAgent = CCPayload.builder().healPayload(componentAgentPayload).entityType(AdapterConstants.AGENT).build();
            ccPayloads.add(caAgent);
        }
        if (!agentVsCreationPayload.containsKey(lfAgentIdentifier) && !agentIdentifiers.contains(lfAgentIdentifier)) {
            HealAgentPayload lfAgentPayload = ccPayloadUtility.getHealAgentPayload(lfAgentIdentifier, service.getIdentifier(), ipAddress, AdapterConstants.LOG_AGENT,
                    this.parameters.getOrDefault(AdapterConstants.HEAL_GRPCENDPT_ADDR, ""), this.parameters.getOrDefault(AdapterConstants.HEAL_GRPCENDPT_PORT, ""));

            CCPayload lfAgent = CCPayload.builder().healPayload(lfAgentPayload).entityType(AdapterConstants.AGENT).build();
            ccPayloads.add(lfAgent);
        }

        return ccPayloads;
    }

    private void updateDomainEntitiesTable(TopologyDetails.Instance instance, String entityType) {
        log.info("Updating domain_entities table for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
        if (!domainEntities.containsKey(instance.getIdentifier())) {
            connectorKpiMaster.addDomainEntities(instance.getIdentifier(), entityType, this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
        }
    }

    private HealInstanceUpdatePayload updateServiceInstanceMappingInHeal(TopologyDetails.Instance host, TopologyDetails.Service service, CompInstClusterDetails existingHostInstance) {
        //Update instance API and add to mapper table
        ServicePojo servicePojo = ServicePojo.builder().name(service.getIdentifier()).identifier(service.getIdentifier()).action("add").build();

        List<BasicEntity> applicationServices = serviceRepo.getApplicationForService(this.parameters.get(AdapterConstants.ACCOUNT_NAME), service.getIdentifier());
        if (applicationServices == null || applicationServices.isEmpty()) {
            log.error("Applications mapped to service {} unavailable. Skipping mapping of service to host {} for jobId:{}, connector instance:{}", service.getIdentifier(), host.getIdentifier(), jobId, connectorInstanceIdentifier);
            return null;
        }

        ApplicationPojo applicationPojo = ApplicationPojo.builder()
                .identifier(applicationServices.get(0).getIdentifier())
                .name(applicationServices.get(0).getName())
                .service(Collections.singletonList(servicePojo)).build();

        List<ApplicationPojo> applicationAttributeList = new ArrayList<>();
        applicationAttributeList.add(applicationPojo);

        HealInstanceUpdatePayload healInstanceUpdatePayload = ccPayloadUtility.getHealInstanceUpdatePayload(existingHostInstance.getId(), host.getIdentifier(), applicationAttributeList);

        log.info("Host update payload: {} for jobId:{}, connector instance:{}", healInstanceUpdatePayload, jobId, connectorInstanceIdentifier);
        return healInstanceUpdatePayload;
    }

    private Map<String, Set<String>> getServiceConnectionEntities(List<TopologyDetails> serviceDetails) {
        Map<String, Set<String>> serviceConnectionMapping = new HashMap<>();

        serviceDetails.stream().map(TopologyDetails::getService).forEach(service -> {
            try {
                if (service.getDesConnections() == null || service.getDesConnections().isEmpty()) {
                    log.info("ToRelationships unavailable for service:{}, jobId:{}, connector instance:{}", service.getIdentifier(), jobId, connectorInstanceIdentifier);
                } else {
                    serviceConnectionMapping.computeIfAbsent(service.getIdentifier(), key -> new HashSet<>()).addAll(service.getDesConnections());
                }
            } catch (Exception e) {
                log.error("Exception occurred while getting service connection entities : {} for jobId:{}, connector instance:{}", e.getMessage(), jobId, connectorInstanceIdentifier, e);
                healthMetrics.putInTransformerErrors(Constants.TOPOLOGY_TRANSFORMER_ERRORS, 1);
            }
        });
        return serviceConnectionMapping;
    }
}
