package com.heal.etladapter.transformers;

import com.appnomic.appsone.common.protbuf.A1EventProtos;
import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.common.protbuf.CommandResponseProtos;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.ByteString;
import com.heal.etladapter.aop.LogExecutionAnnotation;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.ForensicItem;
import com.heal.etladapter.pojos.ForensicOutput;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.zip.GZIPOutputStream;

/**
 * HealForensicsTransformer is responsible for transforming forensic data into A1Event protobuf objects.
 *
 * This transformer processes ForensicOutput objects containing command requests and forensic items,
 * and converts them into A1Event objects suitable for downstream processing in the ETL pipeline.
 *
 * Key Features:
 * - JSON serialization of forensic items for structured data representation
 * - GZIP compression to reduce data size for efficient storage and transmission
 * - Base64 encoding to ensure safe text-based data transfer
 * - CommandResponse creation with compressed forensic data as cmdOut
 * - A1Event wrapping for integration with the broader event processing system
 *
 * Data Flow:
 * ForensicOutput -> JSON Serialization -> GZIP Compression -> Base64 Encoding -> CommandResponse -> A1Event
 *
 * The transformer follows the established pattern of extending AbstractTransformer and implements
 * the required initialize() and transform() methods for proper ETL pipeline integration.
 */
@Slf4j
@Component
public class HealForensicsTransformer extends AbstractTransformer<ForensicOutput, List<A1EventProtos.A1Event>> {

    final private ObjectMapper objectMapper;

    @Autowired
    private AdapterHealthMetrics healthMetrics;

    public HealForensicsTransformer(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Initializes the HealForensicsTransformer.
     *
     * This method performs essential validation and setup required for the transformer to function properly.
     * It validates that the ObjectMapper dependency is available for JSON serialization of forensic items
     * and logs configuration parameters for debugging purposes.
     *
     * @throws EtlAdapterException if ObjectMapper is not available or initialization fails
     */
    @Override
    @LogExecutionAnnotation
    public void initialize() throws EtlAdapterException {

        log.info("Initialization for transformer {} - BEGIN", this.className);

        try {
            // Validate ObjectMapper is available for JSON serialization
            if (objectMapper == null) {
                throw new EtlAdapterException("ObjectMapper is not available for JSON serialization");
            }

            // Log configuration parameters if available
            if (this.parameters != null && !this.parameters.isEmpty()) {
                log.info("Transformer parameters: {}, jobId:{}, connector instance:{}", this.parameters, jobId, connectorInstanceIdentifier);
            }

            log.info("Forensic transformer initialized successfully for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);

        } catch (EtlAdapterException e) {
            log.error("Error in initializing transformer {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier, e);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_INITIALIZATION_ERROR, 1);
            throw new EtlAdapterException("Failed to initialize forensic transformer: " + e.getMessage());
        }

        log.info("Initialization done for transformer {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier);
    }

    /**
     * Transforms ForensicOutput into a list of A1Event protobuf objects.
     *
     * This method processes forensic data by:
     * 1. Extracting CommandRequest and forensic items from the input
     * 2. Serializing forensic items to JSON format
     * 3. Compressing the JSON data using GZIP
     * 4. Encoding the compressed data with Base64
     * 5. Creating CommandResponse objects with the encoded data as cmdOut
     * 6. Wrapping each CommandResponse in an A1Event for downstream processing
     *
     * The compression and encoding process follows the sequence: JSON -> GZIP -> Base64
     * This ensures efficient storage and transmission of potentially large forensic datasets.
     *
     * @param forensicOutput the input containing CommandRequest and list of ForensicItem objects
     * @return List of A1Event objects, one for each command in the request, or empty list on error
     */
    @Override
    @LogExecutionAnnotation
    public List<A1EventProtos.A1Event> transform(ForensicOutput forensicOutput) throws EtlAdapterException {
        try {
            CommandRequestProtos.CommandRequest commandRequest = forensicOutput.getCommandRequest();
            List<CommandRequestProtos.Command> commands = commandRequest.getCommandsList();
            List<ForensicItem> forensicItemList = forensicOutput.getForensicItemList();

            // Record received count for health metrics
            healthMetrics.putInTransformerReceivedCount(this.className, forensicItemList.size());

            // Serialize forensic items to JSON and then compress + base64 encode
            String forensicItemsJson = serializeForensicItems(forensicItemList);
            String compressedAndEncodedForensicItems = compressAndBase64Encode(forensicItemsJson);

            return commands.stream().map(command -> {
                // Create metadata map
                Map<String, String> metadata = new HashMap<>();
                metadata.put("forensicItemCount", String.valueOf(forensicItemList.size()));
                metadata.put("commandJobId", command.getCommandJobId());

                // Build CommandResponse with compressed forensic data
                CommandResponseProtos.CommandResponse commandResponse = CommandResponseProtos.CommandResponse
                        .newBuilder()
                        .setSupervisorIdentifier(commandRequest.getSupervisorIdentifiers(0))
                        .setAgentIdentifier(commandRequest.getAgentIdentifier())
                        .setAgentType(commandRequest.getAgentType())
                        .setCommandJobId(command.getCommandJobId())
                        .setTriggerSource(commandRequest.getTriggerSource())
                        .setCommandType(command.getCommandType())
                        .setCommand(command.getCommand())
                        .setCmdOut(compressedAndEncodedForensicItems) // Gzipped + Base64 encoded forensic data
                        .setStdErr("") // Empty error output
                        .setExitCode(0)
                        .setCommandStartTime(commandRequest.getTriggerTime())
                        .setCommandCompleteTime(System.currentTimeMillis())
                        .putAllMetadata(metadata)
                        .build();

                // Build A1Event with the CommandResponse
                A1EventProtos.A1Event a1Event = A1EventProtos.A1Event.newBuilder()
                        .setEventType(Constants.FORENSIC_OUTPUT)
                        .setEventData(commandResponse.toByteString())
                        .build();

                log.debug("Successfully transformed forensic output for command: {}", command.getCommandJobId());
                return a1Event;
            }).toList();
        } catch (JsonProcessingException e) {
            log.error("Error serializing forensic data", e);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_ERROR, 1);
            throw new EtlAdapterException("Error serializing forensic data: " + e.getMessage());
        } catch (IOException e) {
            log.error("Error compressing or encoding forensic data", e);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_ERROR, 1);
            throw new EtlAdapterException("Error compressing or encoding forensic data: " + e.getMessage());
        } catch (EtlAdapterException e) {
            log.error("Error in forensic transformer", e);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_FORENSIC_TRANSFORMER_ERROR, 1);
            throw new EtlAdapterException("Error in forensic transformer: " + e.getMessage());
        }
    }

    /**
     * Serializes a list of ForensicItem objects to JSON string format.
     *
     * This method converts the forensic items collection into a JSON representation
     * that can be compressed and transmitted efficiently. The JSON format preserves
     * all forensic data structure and allows for easy deserialization on the receiving end.
     *
     * @param forensicItemList the list of ForensicItem objects to serialize
     * @return JSON string representation of the forensic items
     * @throws JsonProcessingException if JSON serialization fails
     */
    private String serializeForensicItems(List<ForensicItem> forensicItemList) throws JsonProcessingException {
        return objectMapper.writeValueAsString(forensicItemList);
    }

    /**
     * Compresses data using GZIP and then encodes with Base64.
     *
     * This method implements a two-step compression and encoding process:
     * 1. GZIP Compression: Reduces the size of the JSON data significantly
     * 2. Base64 Encoding: Converts binary compressed data to text format for safe transmission
     *
     * The process follows the sequence: Input Data -> GZIP Compression -> Base64 Encoding
     * This approach is particularly effective for large forensic datasets, often achieving
     * significant size reduction while maintaining data integrity.
     *
     * @param data the input string data to compress and encode
     * @return Base64 encoded string of the GZIP compressed data
     * @throws IOException if compression or encoding fails
     */
    private String compressAndBase64Encode(String data) throws EtlAdapterException, IOException {
        // Step 1: Compress with GZIP
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
            gzipOut.write(data.getBytes("UTF-8"));
        }
        byte[] compressedData = baos.toByteArray();

        // Step 2: Encode with Base64
        String base64Encoded = Base64.getEncoder().encodeToString(compressedData);

        log.trace("Original size: {} bytes, Compressed size: {} bytes, Base64 size: {} bytes",
                 data.length(), compressedData.length, base64Encoded.length());

        return base64Encoded;
    }
}