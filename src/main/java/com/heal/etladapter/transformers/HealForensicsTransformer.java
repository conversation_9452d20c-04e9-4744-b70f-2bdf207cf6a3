package com.heal.etladapter.transformers;

import com.appnomic.appsone.common.protbuf.A1EventProtos;
import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.common.protbuf.CommandResponseProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.ByteString;
import com.heal.etladapter.pojos.ForensicItem;
import com.heal.etladapter.pojos.ForensicOutput;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.GZIPOutputStream;

@Slf4j
@Component
public class HealForensicsTransformer extends AbstractTransformer<ForensicOutput, List<A1EventProtos.A1Event>> {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void initialize() throws Exception {

    }

    @Override
    public List<A1EventProtos.A1Event> transform(ForensicOutput forensicOutput) {
        try {
            CommandRequestProtos.CommandRequest commandRequest = forensicOutput.getCommandRequest();
            List<CommandRequestProtos.Command> commands = commandRequest.getCommandsList();
            List<ForensicItem> forensicItemList = forensicOutput.getForensicItemList();

            return commands.stream().map(command -> {
                try {
                    // Serialize forensic items to JSON and then compress + base64 encode
                    String forensicItemsJson = serializeForensicItems(forensicItemList);
                    String compressedAndEncodedForensicItems = compressAndBase64Encode(forensicItemsJson);

                    // Create metadata map
                    Map<String, String> metadata = new HashMap<>();
                    metadata.put("forensicItemCount", String.valueOf(forensicItemList.size()));

                    // Build CommandResponse with compressed forensic data
                    CommandResponseProtos.CommandResponse commandResponse = CommandResponseProtos.CommandResponse
                            .newBuilder()
                            .setSupervisorIdentifier(commandRequest.getSupervisorIdentifiers(0))
                            .setAgentIdentifier(commandRequest.getAgentIdentifier())
                            .setAgentType(commandRequest.getAgentType())
                            .setCommandJobId(command.getCommandJobId())
                            .setTriggerSource(commandRequest.getTriggerSource())
                            .setCommandType(command.getCommandType())
                            .setCommand(command.getCommand())
                            .setCmdOut(compressedAndEncodedForensicItems) // Gzipped + Base64 encoded forensic data
                            .setStdErr("") // Empty error output
                            .setExitCode(0)
                            .setCommandStartTime(commandRequest.getTriggerTime())
                            .setCommandCompleteTime(System.currentTimeMillis())
                            .putAllMetadata(metadata)
                            .build();

                    // Build A1Event with the CommandResponse
                    A1EventProtos.A1Event a1Event = A1EventProtos.A1Event.newBuilder()
                            .setEventType(Constants.FORENSIC_OUTPUT)
                            .setEventData(commandResponse.toByteString())
                            .build();

                    log.debug("Successfully transformed forensic output for command: {}", command.getCommandJobId());
                    return a1Event;

                } catch (Exception e) {
                    log.error("Error transforming forensic output for command: {}", command.getCommandJobId(), e);
                    // Return a minimal A1Event in case of error
                    return A1EventProtos.A1Event.newBuilder()
                            .setEventType(Constants.FORENSIC_OUTPUT)
                            .setEventData(ByteString.copyFromUtf8("Error processing forensic data"))
                            .build();
                }
            }).toList();

        } catch (Exception e) {
            log.error("Error in forensic transformer", e);
            return List.of();
        }
    }

    /**
     * Serialize forensic items to JSON string
     */
    private String serializeForensicItems(List<ForensicItem> forensicItemList) throws Exception {
        try {
            return objectMapper.writeValueAsString(forensicItemList);
        } catch (Exception e) {
            log.error("Error serializing forensic items to JSON", e);
            throw new Exception("Failed to serialize forensic items", e);
        }
    }

    /**
     * Compress data using GZIP and then encode with Base64
     * Process: Data -> GZIP -> Base64
     */
    private String compressAndBase64Encode(String data) throws Exception {
        try {
            // Step 1: Compress with GZIP
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
                gzipOut.write(data.getBytes("UTF-8"));
            }
            byte[] compressedData = baos.toByteArray();

            // Step 2: Encode with Base64
            String base64Encoded = Base64.getEncoder().encodeToString(compressedData);

            log.trace("Original size: {} bytes, Compressed size: {} bytes, Base64 size: {} bytes",
                     data.length(), compressedData.length, base64Encoded.length());

            return base64Encoded;

        } catch (IOException e) {
            log.error("Error compressing and encoding data", e);
            throw new Exception("Failed to compress and encode data", e);
        }
    }
}