package com.heal.etladapter.transformers;

import com.heal.etladapter.exceptions.EtlAdapterException;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Will set destination item as source item.
 * 
 * <AUTHOR>
 *
 */
@Component
public class PassThroughTransformer extends AbstractTransformer<List<Object>, List<Object>> {

	@Override
	public void initialize() throws EtlAdapterException {

	}

	@Override
	public List<Object> transform(List<Object> items) {
		return items;
	}

}
