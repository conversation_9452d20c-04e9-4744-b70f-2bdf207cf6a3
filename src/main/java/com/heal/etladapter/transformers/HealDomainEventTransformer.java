package com.heal.etladapter.transformers;

import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.util.DateHelper;
import com.heal.etladapter.beans.*;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.repo.redis.InstanceRepo;
import com.heal.etladapter.repo.redis.ServiceRepo;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Component
public class HealDomainEventTransformer extends AbstractTransformer<List<EventData>, List<Anomalies>> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private ConnectorKpiMaster connectorKpiMaster;
    @Autowired
    private InstanceRepo instanceRepo;
    @Autowired
    private ServiceRepo serviceRepo;

    private Pattern pattern;
    private Map<String, KpiDetail> healKpis = new HashMap<>();

    private List<String> healServices = new ArrayList<>();

    private Map<String, CompInstClusterDetails> healInstances = new HashMap<>();


    private Map<String, DomainHealInstance> instanceMap = new HashMap<>();
    private Map<String, DomainToHealKpiMappings> kpiMappings = new HashMap<>();

    @Override
    public void initialize() throws Exception {
        try {
            if (this.parameters.get(AdapterConstants.DATA_REGEX) == null) {
                log.error("'data.regex' configuration unavailable. Available worker_parameters: {}, jobId:{}, connector instance:{}", this.parameters, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR, 1);
                throw new EtlAdapterException("data.regex configuration missing for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            pattern = Pattern.compile(this.parameters.get(AdapterConstants.DATA_REGEX));

            if (this.parameters.get(AdapterConstants.VALUE) == null
                    || this.parameters.get(AdapterConstants.THRESHOLD) == null
                    || this.parameters.get(AdapterConstants.OPERATION_TYPE) == null) {
                log.error("At least one of 'value', 'threshold', 'operation.type' configurations must be available. Available worker_parameters: {}, jobId:{}, connector instance:{}", this.parameters, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR, 1);
                throw new EtlAdapterException("At least one of 'value', 'threshold', 'operation.type' configurations must be available for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                    .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (r, v) -> r));


            healServices = serviceRepo.getAccountWiseServices(this.parameters.get(AdapterConstants.ACCOUNT_NAME)).stream().map(BasicEntity::getIdentifier).collect(Collectors.toList());

            if (healServices.isEmpty()) {
                log.error("Unable to get list of services from redis for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR, 1);
                throw new EtlAdapterException("Unable to get list of services from redis jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            healInstances = instanceRepo.getInstancesByAccount(this.parameters.get(AdapterConstants.ACCOUNT_NAME)).stream().collect(Collectors.toMap(CompInstClusterDetails::getIdentifier, Function.identity(), (r, v) -> r));

            if (healInstances.isEmpty()) {
                log.error("Unable to get list of instances from redis for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR, 1);
                throw new EtlAdapterException("Unable to get list of instances from redis jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            if (instanceMap.isEmpty()) {
                log.error("Instances unavailable. Please check the mapper table: a1_logscan_heal_instance_mapper for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR, 1);
                throw new EtlAdapterException("Instances unavailable in instance mapper table jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            List<KpiDetail> healKpiList = connectorKpiMaster.getHealKPIs(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
            healKpis = new HashMap<>();
            for (KpiDetail kpi : healKpiList) {
                healKpis.put(kpi.getIdentifier(), kpi);
            }

            if (healKpis.isEmpty()) {
                healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR, 1);
                log.error("Heal KPI details corresponding to domain KPI unavailable. Failing the initialization for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                throw new EtlAdapterException("Heal KPI details corresponding to domain KPI unavailable jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            kpiMappings = connectorKpiMaster.getDomainToHealKpiMappings(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                    .collect(Collectors.toMap(DomainToHealKpiMappings::getSrcKpiIdentifier, p -> p));

            if (kpiMappings.isEmpty()) {
                healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_CONFIGURATION_ERROR, 1);
                log.error("Heal to domain KPI mapping is unavailable. Failing the initialization for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                throw new EtlAdapterException("Heal to domain KPI mapping is unavailable jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

        } catch (Exception e) {
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_INITIALIZATION_ERROR, 1);
            log.error("Exception while initializing {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier, e);
            throw new EtlAdapterException(e.getMessage() + "jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
    }

    @Override
    public List<Anomalies> transform(List<EventData> items) {

        if (items == null || items.isEmpty()) {
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_ERROR, 1);
            log.info("No items in transformer to process the data for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }

        healthMetrics.putInTransformerReceivedCount(this.className, items.size());

        log.info("Event Data transformation started, number of items:{}, jobId:{}, connector instance:{}", items.size(), jobId, connectorInstanceIdentifier);

        List<Anomalies> eventsProto = new ArrayList<>();
        items.forEach(eventData -> {
            try {
                if (eventData == null) {
                    healthMetrics.putInTransformerDropCount(this.className, 1);
                    log.info("No items in src item to process the data. Dropping the event: {}, jobId:{}, connector instance:{}", eventData, jobId, connectorInstanceIdentifier);
                    return;
                }

                log.trace("Transforming event: {} for jobId:{}, connector instance:{}", eventData, jobId, connectorInstanceIdentifier);

                KpiDetail healKpi = null;

                if (eventData.getKpiId() == null) {
                    healthMetrics.putInTransformerDropCount(this.className, 1);
                    log.error("KPI ID is unavailable. Dropping the event: {}, jobId:{}, connector instance:{}", eventData, jobId, connectorInstanceIdentifier);
                    return;
                }

                if (eventData.getServiceIdentifier() == null || eventData.getServiceIdentifier().isEmpty()) {
                    healthMetrics.putInTransformerDropCount(this.className, 1);
                    log.error("Service identifier is unavailable. Dropping the event: {}, jobId:{}, connector instance:{}", eventData, jobId, connectorInstanceIdentifier);
                    return;
                }

                DomainToHealKpiMappings kpi = kpiMappings.get(eventData.getKpiIdentifier());
                if (kpi != null) {
                    healKpi = healKpis.get(kpi.getHealKpiIdentifier());
                }

                if (null == healKpi) {
                    healthMetrics.putInTransformerDropCount(this.className, 1);
                    log.error("Heal KPI unavailable for KPI. Dropping the event: {}, jobId:{}, connector instance:{}", eventData, jobId, connectorInstanceIdentifier);
                    return;
                }

                log.debug("Heal KpiId mapped to event KpiId: {}, event: {}, healKpi: {}, jobId:{}, connector instance:{}", healKpi.getId(), items, healKpi, jobId, connectorInstanceIdentifier);

                String instanceID = "";

                if (eventData.getComponentInstanceIdentifier() != null && !eventData.getComponentInstanceIdentifier().isEmpty()) {
                    instanceID = this.instanceMap.containsKey(eventData.getComponentInstanceIdentifier())
                            ? this.instanceMap.get(eventData.getComponentInstanceIdentifier()).getHealInstanceName()
                            : this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_INSTANCE, eventData.getComponentInstanceIdentifier());
                }

                if (instanceID.isEmpty() && eventData.getHostInstanceIdentifier() != null && !eventData.getHostInstanceIdentifier().isEmpty()) {
                    instanceID = this.instanceMap.containsKey(eventData.getHostInstanceIdentifier())
                            ? this.instanceMap.get(eventData.getHostInstanceIdentifier()).getHealInstanceName()
                            : this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_INSTANCE, eventData.getHostInstanceIdentifier());
                }

                if (instanceID.equals(this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_INSTANCE, ""))) {
                    log.warn("Instance is not configured in HEAL for event:{}, jobId:{}, connector instance:{}", eventData, jobId, connectorInstanceIdentifier);
                }

                if (!healInstances.containsKey(instanceID)) {
                    log.error("The instance {} is not available in Heal. Hence, skipping the Event : {}, jobId:{}, connector instance:{}", instanceID, eventData, jobId, connectorInstanceIdentifier);
                    return;
                }
                String serviceID = Optional.ofNullable(eventData.getServiceIdentifier())
                        .filter(id -> !id.isEmpty())
                        .map(id -> healServices.contains(id) ? id
                                : this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_SERVICE, id))
                        .orElse("");

                if (!healServices.contains(serviceID)) {
                    log.error("The service {} is not available in Heal. Hence, skipping the Event : {}, jobId:{}, connector instance:{}", serviceID, eventData, jobId, connectorInstanceIdentifier);
                    return;
                }

                String applicationId = Optional.ofNullable(eventData.getApplicationIdentifier())
                        .filter(id -> !id.isEmpty())
                        .orElse(this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_APP, ""));

                long applicationCount = serviceRepo.getApplicationForService(this.parameters.get(AdapterConstants.ACCOUNT_NAME), serviceID)
                        .stream()
                        .filter(app -> app.getIdentifier().equalsIgnoreCase(applicationId))
                        .count();

                if (applicationCount == 0) {
                    log.error("Application not present in redis. Hence, skipping the Event for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                    return;
                }

                Anomalies anomalyData = new Anomalies();
                String operationType = extractDataValue(eventData.getDescription(), this.parameters.get(AdapterConstants.OPERATION_TYPE), pattern).trim();
                anomalyData.setAnomalyId(eventData.getEventId());
                anomalyData.setKpiAttribute(eventData.getKpiAttribute());
                anomalyData.setKpiId(healKpi.getId());
                anomalyData.setKpiIdentifier(healKpi.getName());
                anomalyData.setThresholdType("Realtime");
                anomalyData.setSeverity(eventData.getThresholdSeverity());
                anomalyData.setServiceId(Collections.singleton(serviceID));
                anomalyData.setOperationType(operationType);

                if (this.parameters.get(AdapterConstants.DOMAIN).equalsIgnoreCase("dynatrace")) {
                    anomalyData.setValue(String.valueOf(eventData.getActualValue()));
                } else {
                    anomalyData.setValue(extractDataValue(eventData.getDescription(), this.parameters.get(AdapterConstants.VALUE), pattern).trim());
                }

                anomalyData.setAnomalyTime(eventData.getEventStartTime());
                anomalyData.setTimestamp(DateHelper.getDate(eventData.getEventStartTime()));

                Map<String, Double> thresholdData = new HashMap<>();

                if (eventData.getThreshold() > 0.0) {
                    thresholdData.put("Lower", eventData.getThreshold());
                } else if (operationType.contains("greater") || operationType.contains("less")) {
                    if (this.parameters.get(AdapterConstants.DOMAIN).equalsIgnoreCase("dynatrace")) {
                        thresholdData.put("Lower", eventData.getThreshold());
                    } else {
                        thresholdData.put("Lower", Double.valueOf(extractDataValue(eventData.getDescription(), this.parameters.get(AdapterConstants.THRESHOLD), pattern).trim()));
                    }
                }
                anomalyData.setThresholds(thresholdData);

                if (eventData.isTransactionEvent()) {
                    anomalyData.setTransactionId(eventData.getTransactionIdentifier());
                    Map<String, String> metadata = getAnomalyMetadata(applicationId, "txn", eventData);
                    anomalyData.setMetadata(metadata);
                } else {
                    anomalyData.setInstanceId(instanceID);
                    Map<String, String> metadata = getAnomalyMetadata(applicationId, "kpi", eventData);
                    anomalyData.setMetadata(metadata);
                }

                log.debug("Event Proto = {}", anomalyData);

                eventsProto.add(anomalyData);
            } catch (Exception e) {
                healthMetrics.putInTransformerDropCount(this.className, 1);
                log.error("Error occurred while processing the transformation for item:{}, jobId:{}, connector instance:{}", eventData, jobId, connectorInstanceIdentifier, e);
            }
        });

        log.info("Transformed events count: {}, jobId:{}, connector instance:{}", eventsProto.size(), jobId, connectorInstanceIdentifier);

        return eventsProto;
    }

    @NotNull
    private Map<String, String> getAnomalyMetadata(String applicationId, String eventDatatype, EventData eventData) {
        Map<String, String> metadata = new HashMap<>();
        metadata.put("accountId", this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        metadata.put("appId", applicationId);
        metadata.put("source", this.parameters.get(AdapterConstants.DOMAIN));
        metadata.put("status", eventData.getIncidentStatus());
        metadata.put("eventDataType", eventDatatype);
        metadata.put("anomalyEndTime", String.valueOf(eventData.getEventEndTime()));
        metadata.put("description", eventData.getDescription() != null ? eventData.getDescription() : "");
        metadata.put("extractorName", eventData.getExtractorName());
        metadata.put("KPI_VIOLATION_TIME", com.heal.etladapter.utility.DateHelper.date2stringConverter(new Date(eventData.getEventStartTime())));
        metadata.put(AdapterConstants.FROM_TIME, String.valueOf(eventData.getFromTime()));
        metadata.put(AdapterConstants.TO_TIME, String.valueOf(eventData.getToTime()));

        log.debug("Metadata : {}", metadata);

        return metadata;
    }

    //TODO: KRK to check if we need to drop the data in case of negative output
    private String extractDataValue(String path, String name, Pattern pattern) {
        try {
            Matcher m = pattern.matcher(path);
            if (m.find()) {
                String retVal = m.group(name);
                if (retVal.equalsIgnoreCase("increased")) {
                    return "greater than";
                } else if (retVal.equalsIgnoreCase("decreased")) {
                    return "lesser than";
                }
            } else {
                log.error("Regex pattern failed to find a match in {} for jobId:{}, connector instance:{}", path, jobId, connectorInstanceIdentifier);
                return AdapterConstants.DATA_NOT_FOUND;
            }
        } catch (Exception e) {
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_EVENT_TRANSFORMER_ERROR, 1);
            log.error("Error occurred while extracting the data from pattern, path:{}, name:{}, pattern:{}, jobId:{}, connector instance:{}", path, name, pattern, jobId, connectorInstanceIdentifier, e);
        }
        return AdapterConstants.DATA_NOT_FOUND;
    }
}
