package com.heal.etladapter.transformers;

import com.appnomic.appsone.common.protbuf.A1EventProtos;
import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.common.protbuf.CommandResponseProtos;
import com.heal.etladapter.pojos.ForensicItem;
import com.heal.etladapter.pojos.ForensicOutput;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class HealForensicsTransformerExisting extends AbstractTransformer<ForensicOutput, List<A1EventProtos.A1Event>> {

    @Override
    public void initialize() throws Exception {

    }

    @Override
    public List<A1EventProtos.A1Event> transform(ForensicOutput forensicOutput) {
        CommandRequestProtos.CommandRequest commandRequest = forensicOutput.getCommandRequest();

        List<CommandRequestProtos.Command> commands = commandRequest.getCommandsList();

        List<ForensicItem> forensicItemList = forensicOutput.getForensicItemList();

        A1EventProtos.A1Event event = commands.stream().map(command -> {
            CommandResponseProtos.CommandResponse
                    .newBuilder()
                    //Assuming that there is only supervisor since connector collects the forensics in this case.
                    .setSupervisorIdentifier(commandRequest.getSupervisorIdentifiers(0))
                    .setCommandJobId(command.getCommandJobId())
                    .setCommand(command.getCommand())
                    .setAgentIdentifier(commandRequest.getAgentIdentifier())
                    .setAgentType(commandRequest.getAgentType())
                    .setCommandType(command.getCommandType())
                    .setTriggerSource(commandRequest.getTriggerSource())
                    .setCommandCompleteTime(System.currentTimeMillis())
                    .setExitCode(0)
                    .setCmdOut(cmdOut)
                    .setStdErr(stdErr)
                    .putAllMetadata(metadata);

            commandResponseBuilder.setCommandType(Constants.FORENSIC_OUTPUT);
            commandResponseBuilder.setCmdOut(forensicOutput.toString());

            A1EventProtos.A1Event.Builder a1EventBuilder = A1EventProtos.A1Event.newBuilder();
            a1EventBuilder.setEventType(Constants.FORENSIC_OUTPUT);
            a1EventBuilder.setEventData(commandResponseBuilder.build().toByteString());
        }).toList();


        String encodedCmdOutput = forensicCommandHelper.getCommandOutput() == null
                ? Utility.compressAndBase64Encode("") : Utility.compressAndBase64Encode(forensicCommandHelper.getCommandOutput());
        log.trace("EncodedCmdOutput: {}", encodedCmdOutput);
        String errorOutput = forensicCommandHelper.getErrorOutput() == null
                ? Utility.compressAndBase64Encode("") : Utility.compressAndBase64Encode(forensicCommandHelper.getErrorOutput());
        log.trace("EncodedErrorOutput: {}", errorOutput);

        CommandResponseProtos.CommandResponse commandResponse = CommandResponseProtos.CommandResponse
                .newBuilder()
                .setSupervisorIdentifier(command.getSupervisorIdentifier())
                .setCommandJobId(command.getCommandJobID())
                .setCommand(command.getCommand())
                .setAgentIdentifier(command.getAgentIdentifier())
                .setAgentType(command.getAgentType())
                .setCommandType(command.getCommandType())
                .setTriggerSource(command.getTriggerSource())
                .setCommandStartTime(command.getTriggerTime())
                .setCommandCompleteTime(commandCompleteTime)
                .setExitCode(forensicCommandHelper.getExitCode())
                .setCmdOut(encodedCmdOutput)
                .setStdErr(errorOutput)
                .putAllMetadata(command.getMetaData())
                .build();

        A1EventProtos.A1Event event = A1EventProtos.A1Event.newBuilder()
                .setEventType(Constants.FORENSIC_OUTPUT)
                .setEventData(commandResponse.toByteString())
                .build();

        //TODO - map all the required values from CommandRequest to CommandResponse Proto
        // - Base64 encode and Gzip the forensicItem before setting it as cmdOut(first ggip and then base64)
        CommandResponseProtos.CommandResponse.Builder commandResponseBuilder = CommandResponseProtos.CommandResponse
                .newBuilder()
                //Assuming that there is only supervisor since connector collects the forensics in this case.
                .setSupervisorIdentifier(commandRequest.getSupervisorIdentifiers(0))
                .setCommandJobId(commandRequest.getCom)
                .setCommand(jsonObject.getString(Constants.COMMAND))
                .setAgentIdentifier(jsonObject.getString(Constants.AGENT_IDENTIFIER))
                .setAgentType(jsonObject.getString(Constants.AGENT_TYPE))
                .setCommandType(jsonObject.getString(Constants.COMMAND_TYPE))
                .setTriggerSource(jsonObject.getString(Constants.TRIGGER_SOURCE))
                .setCommandStartTime(jsonObject.getLong(Constants.COMMAND_START_TIME))
                .setCommandCompleteTime(commandCompleteTime)
                .setExitCode(jsonObject.getInt(Constants.EXIT_CODE))
                .setCmdOut(cmdOut)
                .setStdErr(stdErr)
                .putAllMetadata(metadata);

        commandResponseBuilder.setCommandType(Constants.FORENSIC_OUTPUT);
        commandResponseBuilder.setCmdOut(forensicOutput.toString());

        A1EventProtos.A1Event.Builder a1EventBuilder = A1EventProtos.A1Event.newBuilder();
        a1EventBuilder.setEventType(Constants.FORENSIC_OUTPUT);
        a1EventBuilder.setEventData(commandResponseBuilder.build().toByteString());

        return a1EventBuilder.build();
    }
}