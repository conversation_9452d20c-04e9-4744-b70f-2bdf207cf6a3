package com.heal.etladapter.transformers;

import com.appnomic.appsone.common.protbuf.CollatedTransactionsProtos;
import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos;
import com.appnomic.appsone.transaction.mapper.MappedTxn;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.Rule;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.repo.redis.ServiceRepo;
import com.heal.etladapter.service.TransactionMapperService;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.DateHelper;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

@Slf4j
@Component
public class HealCollatedTransactionTransformer extends AbstractTransformer<List<Map<String, String>>, List<CollatedTransactionsProtos.CollatedTransactions>> {

    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    protected ConnectorKpiMaster connectorKpiMaster;
    @Autowired
    protected TransactionMapperService transactionMapperService;
    @Autowired
    private ServiceRepo serviceRepo;

    @Override
    public void initialize() throws EtlAdapterException {
        if (this.getParameters().get(AdapterConstants.ACCOUNT_NAME) == null || this.getParameters().get(AdapterConstants.ACCOUNT_NAME).isEmpty()) {
            log.error("account.name missing in the worker_parameters for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.getParameters());
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_COLLATED_TRANSACTION_TRANSFORMER_CONFIGURATION_ERROR, 1);
            throw new EtlAdapterException("account.name missing in worker_parameters for jobId: " + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (this.getParameters().get(AdapterConstants.BASE_URL) == null || this.getParameters().get(AdapterConstants.BASE_URL).isEmpty()) {
            log.error("base.url missing in the worker_parameters for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.getParameters());
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_COLLATED_TRANSACTION_TRANSFORMER_CONFIGURATION_ERROR, 1);
            throw new EtlAdapterException("base.url missing in worker_parameters for jobId: " + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

    }

    @Override
    public List<CollatedTransactionsProtos.CollatedTransactions> transform(List<Map<String, String>> items) {
        List<CollatedTransactionsProtos.CollatedTransactions> collatedTransactions = new ArrayList<>();
        boolean isURLNormalizeEnabled = Boolean.parseBoolean(this.parameters.getOrDefault(AdapterConstants.URL_NORMALIZE, "true"));
        if (items == null || items.isEmpty()) {
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_COLLATED_TRANSACTION_TRANSFORMER_ERROR, 1);
            log.warn("No items in transformer to process the data for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }

        log.info("Total number of Transaction proto came to Collated Transformer for jobId:{}, connector instance:{}, count: {}",jobId, connectorInstanceIdentifier, items.size());

        healthMetrics.putInTransformerReceivedCount(this.className, items.size());

        items.forEach(rawData -> {
            if (!rawData.containsKey(AdapterConstants.HTTP_URI)) {
                log.error("URI field not present in document: {}. Hence, dropping Item for jobId:{}, connector instance:{}", rawData, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }
            if (!rawData.containsKey((AdapterConstants.HTTP_RESPONSE_TIME))) {
                log.error("Response Time field not present in document: {}. Hence, dropping Item for jobId:{}, connector instance:{}", rawData, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }

            float responseTime;
            try {
                responseTime = Float.parseFloat(rawData.get(AdapterConstants.HTTP_RESPONSE_TIME));
            } catch (NumberFormatException ex) {
                log.error("Exception occurred while casting the response time in data: {}, jobId:{}, connector instance:{}", rawData, jobId, connectorInstanceIdentifier, ex);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }

            String instanceName = rawData.get(AdapterConstants.INSTANCE_NAME);

            String agentUid = StringUtil.EMPTY_STRING;
            List<BasicAgentEntity> agentsForService = serviceRepo.getAgentsForService(this.parameters.get(AdapterConstants.ACCOUNT_NAME), instanceName);
            if (agentsForService != null && !agentsForService.isEmpty()) {
                Optional<BasicAgentEntity> agentEntity = agentsForService.stream().filter(ba -> AdapterConstants.LOG_AGENT.equalsIgnoreCase(ba.getType())).findFirst();
                if (agentEntity.isPresent()) {
                    BasicAgentEntity basicAgentEntity = agentEntity.get();
                    agentUid = basicAgentEntity.getIdentifier();
                } else {
                    log.info("Instance to agent mapping unavailable for instance : {}, jobId:{}, connector instance:{}", instanceName, jobId, connectorInstanceIdentifier);
                }
            }
            if (agentUid.equals(StringUtil.EMPTY_STRING)) {
                agentUid = this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_AGENT, instanceName);
            }

            String httpUri = rawData.get(AdapterConstants.HTTP_URI);
            String transactionID = null;
            try {
                URL url = new URL(httpUri);
                transactionID = url.getPath();
            } catch (MalformedURLException me) {
                log.warn("Got Malformed url! Normalizing Url, item: {}, isURLNormalizeEnabled:{}, jobId:{}, connector instance:{}", rawData, isURLNormalizeEnabled, jobId, connectorInstanceIdentifier);
                if (isURLNormalizeEnabled) {
                    transactionID = httpUri.startsWith("/") ? httpUri : "/" + httpUri;
                }
            }
            log.info("Extracted transactionId : {}, from url : {}, jobId:{}, connector instance:{}", transactionID, httpUri, jobId, connectorInstanceIdentifier);

            if (transactionID == null || transactionID.isEmpty()) {
                log.error("Unable to get the transaction details. Hence, dropping the item {} for jobId:{}, connector instance:{}", rawData, jobId, connectorInstanceIdentifier);
                healthMetrics.putInTransformerDropCount(this.className, 1);
                return;
            }

            try {
                int ruleId = 0;
                String accountIdentifier = this.parameters.get(AdapterConstants.ACCOUNT_NAME);
                List<Rule> serviceWiseRules = serviceRepo.getServiceWiseRules(accountIdentifier, instanceName);
                if (serviceWiseRules != null && !serviceWiseRules.isEmpty()) {
                    // get mapped rule for given txn
                    MappedTxn mappedRuleFromTransactionMapper = transactionMapperService.getMappedRuleFromTransactionMapper(serviceWiseRules, instanceName, transactionID);
                    if (mappedRuleFromTransactionMapper != null && mappedRuleFromTransactionMapper.getRuleId() > 0) {
                        ruleId = mappedRuleFromTransactionMapper.getRuleId();
                    } else {
                        // get default rule
                        Optional<Rule> ruleOpt = serviceWiseRules.stream().filter(ba -> AdapterConstants.FIRST_2_SEGMENTS.equalsIgnoreCase(ba.getName())).findFirst();
                        if (ruleOpt.isPresent()) {
                            Rule rule = ruleOpt.get();
                            ruleId = rule.getId();
                        } else {
                            log.info("Service wise rules are unavailable for accountIdentifier:{}, instance:{}, jobId:{}, connector instance:{}", accountIdentifier, instanceName, jobId, connectorInstanceIdentifier);
                        }
                    }
                }

                if (ruleId == 0) {
                    log.error("Unable to get the rule details for the service. Hence, dropping the item:{}, accountIdentifier:{}, jobId:{}, connector instance:{}", rawData, accountIdentifier, jobId, connectorInstanceIdentifier);
                    healthMetrics.putInTransformerDropCount(this.className, 1);
                    return;
                }

                Date endTimeInGMT = DateHelper.string2ElkDateConverter(rawData.get(AdapterConstants.TIMESTAMP));

                CollatedTransactionsProtos.CollatedTransactions.Builder collatedTransactionBuilder = CollatedTransactionsProtos.CollatedTransactions.newBuilder()
                        .setAgentUid(agentUid)
                        .setStartTimeInGMT(DateHelper.date2GMTString4CollateTxn(endTimeInGMT != null ? endTimeInGMT : new Date()))
                        .setRuleId(ruleId)
                        .setTransactionId(transactionID)
                        .setEnterpriseName(this.getParameters().getOrDefault(AdapterConstants.DOMAIN, "Dynatrace"))
                        .setTimezoneOffsetInSeconds(0);

                PSAgentMessageProtos.ResponseTime.ResponseTimeType responseTimeType = PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC;

                int goodTransactionCount = Integer.parseInt(rawData.get(AdapterConstants.LOGSCAN_SUCCESS_COUNT) != null ? rawData.get(AdapterConstants.LOGSCAN_SUCCESS_COUNT) : "0");
                int failTransactionCount = Integer.parseInt(rawData.get(AdapterConstants.LOGSCAN_FAILURE_COUNT) != null ? rawData.get(AdapterConstants.LOGSCAN_FAILURE_COUNT) : "0");
                int totalCount = goodTransactionCount + failTransactionCount;

                CollatedTransactionsProtos.Response.Builder responseBuilder = CollatedTransactionsProtos.Response.newBuilder()
                        .setResponseTimeType(responseTimeType)
                        .setAvgResponseTimeInMicroseconds(responseTime)
                        .setTotalCount(totalCount);

                if (goodTransactionCount != 0) {
                    responseBuilder.addCounts(CollatedTransactionsProtos.Response.CollatedCount.newBuilder().
                            setResponseStatusTag(PSAgentMessageProtos.ResponseTime.ResponseStatusTag.GOOD).
                            setCount(goodTransactionCount).build());
                }

                if (failTransactionCount != 0) {
                    responseBuilder.addCounts(CollatedTransactionsProtos.Response.CollatedCount.newBuilder().
                            setResponseStatusTag(PSAgentMessageProtos.ResponseTime.ResponseStatusTag.FAIL).
                            setCount(failTransactionCount).build());
                }

                // Building and adding response to the Builder
                CollatedTransactionsProtos.Response response = responseBuilder.build();
                collatedTransactionBuilder.addResponse(response);

                CollatedTransactionsProtos.CollatedTransactions collatedTransaction = collatedTransactionBuilder.build();
                log.debug("rawData: {}, PSAgentMessage : {}, jobId:{}, connector instance:{}", rawData, collatedTransaction, jobId, connectorInstanceIdentifier);

                collatedTransactions.add(collatedTransaction);
            } catch (Exception e) {
                log.error("Exception occurred while processing the Transaction for jobId:{}, connector instance:{}, data: {}",jobId, connectorInstanceIdentifier, rawData, e);
                healthMetrics.putInTransformerDropCount(this.className, 1);
            }
        });

        healthMetrics.putInTransformerProcessedCount(this.className, collatedTransactions.size());

        log.info("Total number of transactions collated for jobId:{}, connector instance:{}, count:{}",jobId, connectorInstanceIdentifier, collatedTransactions.size());

        return collatedTransactions;
    }
}

