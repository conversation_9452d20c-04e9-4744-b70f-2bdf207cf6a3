package com.heal.etladapter.transformers;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.heal.etladapter.beans.*;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.DateHelper;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component
public class HealKPITransformer extends AbstractTransformer<List<DomainKpi>, List<KPIAgentMessageProtos.KPIAgentMessage>> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private ConnectorKpiMaster connectorKpiMaster;

    private Map<Integer, String> kpiTypes = new HashMap<>();
    private final Map<String, KpiDetail> healKpis = new HashMap<>();
    private Map<String, DomainHealInstance> instanceMap = new HashMap<>();
    private Map<String, DomainToHealKpiMappings> kpiMappings = new HashMap<>();
    private Properties errorProperty;

    @Override
    public void initialize() throws Exception {
        long st = System.currentTimeMillis();

        log.info("Initialization for transformer {} - BEGIN", this.className);

        try {
            log.info("Reloading configuration from ConnectorKpiMaster for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);

            if (this.parameters.containsKey(AdapterConstants.DOMAIN) && this.parameters.get(AdapterConstants.DOMAIN).equalsIgnoreCase("Prometheus")) {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .filter(o -> o.getHealAgentUid().endsWith("_kpi") || o.getDomainInstanceName().equalsIgnoreCase("cluster"))
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            } else {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            }

            List<KpiDetail> healKpiList = connectorKpiMaster.getHealKPIs(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
            healKpis.clear();
            for (KpiDetail kpi : healKpiList) {
                healKpis.put(kpi.getIdentifier(), kpi);
            }

            kpiMappings = connectorKpiMaster.getDomainToHealKpiMappings(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                    .collect(Collectors.toMap(DomainToHealKpiMappings::getSrcKpiIdentifier, p -> p, (o, n) -> n));

            kpiTypes = connectorKpiMaster.getKpiTypes(this.jdbcTemplate).stream().collect(Collectors.toMap(KPITypeDetails::getSubTypeId, KPITypeDetails::getName));

            log.info("Instance map size:{}, heal kpis size:{}, kpi mapping size:{}, appsoneKpiTypes: {}, jobId:{}, connector instance:{}", instanceMap.size(), healKpis.size(), kpiMappings.size(), kpiTypes.size(), jobId, connectorInstanceIdentifier);

            FileReader errorFileReader;

            if (!this.parameters.getOrDefault(AdapterConstants.ERROR_PROPERTY, "").trim().isEmpty()) {
                errorFileReader = new FileReader(this.parameters.get(AdapterConstants.ERROR_PROPERTY));
                errorProperty = new Properties();
                errorProperty.load(errorFileReader);
            }
        } catch (Exception e) {
            log.error("Error in initializing transformer {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier, e);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_KPI_TRANSFORMER_INITIALIZATION_ERROR, 1);
            throw new EtlAdapterException(e.getMessage() + " for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (healKpis.isEmpty()) {
            log.error("Heal metrics unavailable to process metrics. Failing the transformer initialization for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_KPI_TRANSFORMER_CONFIGURATION_ERROR, 1);
            throw new EtlAdapterException("Heal metrics details unavailable for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (instanceMap.isEmpty()) {
            log.error("Heal instances mapping is unavailable. Failing the transformer initialization for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            healthMetrics.putInTransformerErrors(AdapterConstants.HEAL_KPI_TRANSFORMER_CONFIGURATION_ERROR, 1);
            throw new EtlAdapterException("Heal instances mapping unavailable for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        log.info("Initialization for transformer {} for jobId:{}, connector instance:{} - END. Time taken: {} ms", this.className, jobId, connectorInstanceIdentifier, (System.currentTimeMillis() - st));
    }

    @Override
    public List<KPIAgentMessageProtos.KPIAgentMessage> transform(List<DomainKpi> items) {
        if (items == null || items.isEmpty()) {
            log.warn("KPI Transformer not received any data from extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            return null;
        }

        healthMetrics.putInTransformerReceivedCount(this.className, items.size());

        Map<String, Map<String, List<KPIAgentMessageProtos.KPIAgentMessage.KpiData>>> agentsDetails = new HashMap<>();
        List<KPIAgentMessageProtos.KPIAgentMessage> agentProtos = new ArrayList<>();
        try {

            if (this.parameters.containsKey(AdapterConstants.DOMAIN) && this.parameters.get(AdapterConstants.DOMAIN).equalsIgnoreCase("Prometheus")) {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .filter(o -> o.getHealAgentUid().endsWith("_kpi") || o.getDomainInstanceName().equalsIgnoreCase("cluster"))
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            } else {
                instanceMap = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate).stream()
                        .collect(Collectors.toMap(DomainHealInstance::getDomainInstanceName, Function.identity(), (o, n) -> n));
            }

            items.forEach(src -> {
                try {
                    try {
                        src.setCollectionInterval((int) (src.getUpperThreshold().getTime() - src.getLowerThreshold().getTime()) / 1000);
                    } catch (Exception e) {
                        log.error("Exception while setting collection for src : {} , jobId:{}, connector instance:{} ", src, jobId, connectorInstanceIdentifier, e);
                    }

                    log.debug("Transforming Domain KPI {} for jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier);

                    KPIAgentMessageProtos.KPIAgentMessage.KpiData laKpiData;

                    KpiDetail kpiDetail = null;
                    DomainToHealKpiMappings kpi = kpiMappings.get(src.getKpiName());
                    if (kpi != null) {
                        kpiDetail = healKpis.get(kpi.getHealKpiIdentifier());
                    }

                    if (null == kpiDetail) {
                        log.error("HEAL KPI unavailable for Domain KPI {}. Discarding the KPI for jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier);
                        healthMetrics.putInTransformerDropCount(this.className, 1);
                        return;
                    }

                    if (!this.instanceMap.containsKey(src.getDomainInstanceId())) {
                        log.warn("HEAL instance unavailable for domain instance: {}. Falling back to default instance identifier, instance map size:{}, jobId:{}, connector instance:{}", src.getDomainInstanceId(), this.instanceMap.size(), jobId, connectorInstanceIdentifier);
                    }

                    log.trace("Begin Transforming Kpi data for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
                    if (kpiDetail.getKpiGroupId() > 0) {
                        laKpiData = getGroupKpiData(src, kpiDetail);
                    } else {
                        laKpiData = getKpiData(src, kpiDetail);
                    }

                    String instanceId = this.instanceMap.containsKey(src.getDomainInstanceId())
                            ? this.instanceMap.get(src.getDomainInstanceId()).getHealInstanceName()
                            : this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_INSTANCE, src.getDomainInstanceId());

                    if (instanceId == null) {
                        log.error("InstanceID is null in the adapterItem source: {}. Hence, skipping the instance data for jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier);
                        return;
                    }

                    String agentUId = StringUtil.EMPTY_STRING;

                    if (this.instanceMap.containsKey(src.getDomainInstanceId())) {
                        agentUId = instanceMap.get(src.getDomainInstanceId()).getHealAgentUid();
                    }
                    if (agentUId.equals(StringUtil.EMPTY_STRING)) {
                        log.warn("Instance to agent mapping unavailable for instance: {}. Falling back to default agent identifier. Instance map size:{}, jobId:{}, connector instance:{}", src.getDomainInstanceId(), instanceMap.size(), jobId, connectorInstanceIdentifier);
                        agentUId = this.parameters.getOrDefault(AdapterConstants.ADAPTER_DEFAULT_AGENT, src.getDomainInstanceId());
                    }

                    if (agentsDetails.containsKey(agentUId)) {
                        if (agentsDetails.get(agentUId).containsKey(instanceId)) {
                            agentsDetails.get(agentUId).get(instanceId).add(laKpiData);
                        } else {
                            List<KPIAgentMessageProtos.KPIAgentMessage.KpiData> kpiData = new ArrayList<>();
                            kpiData.add(laKpiData);
                            agentsDetails.get(agentUId).put(instanceId, kpiData);
                        }
                    } else {
                        List<KPIAgentMessageProtos.KPIAgentMessage.KpiData> kpiData = new ArrayList<>();
                        kpiData.add(laKpiData);
                        Map<String, List<KPIAgentMessageProtos.KPIAgentMessage.KpiData>> instanceData = new HashMap<>();
                        instanceData.put(instanceId, kpiData);
                        agentsDetails.put(agentUId, instanceData);
                    }
                } catch (Exception e) {
                    log.error("Error occurred while for processing the adapter item:{}, jobId:{}, connector instance:{}", src, jobId, connectorInstanceIdentifier, e);
                    healthMetrics.putInTransformerDropCount(this.className, 1);
                }
            });

            agentsDetails.forEach((agentId, instanceDetails) -> {

                KPIAgentMessageProtos.KPIAgentMessage.Builder agentMsgBuilder = KPIAgentMessageProtos.KPIAgentMessage
                        .newBuilder()
                        .setAgentUid(agentId);

                instanceDetails.forEach((instanceId, kpiData) -> {

                    KPIAgentMessageProtos.KPIAgentMessage.Instance.Builder instanceBuilder = KPIAgentMessageProtos.KPIAgentMessage.Instance
                            .newBuilder().addAllKpiData(kpiData)
                            .setInstanceId(instanceId);
                    agentMsgBuilder.addInstances(instanceBuilder.build());
                });

                agentProtos.add(agentMsgBuilder.build());
            });
        } catch (Exception e) {
            log.error("Error occurred while processing KPI transformer for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }

        log.info("Transformation complete for domain KPIs. KPI count: {}, jobId:{}, connector instance:{}", agentProtos.size(), jobId, connectorInstanceIdentifier);

        return agentProtos;
    }

    public KPIAgentMessageProtos.KPIAgentMessage.KpiData getKpiData(DomainKpi src, KpiDetail healKpi) {
        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType kpiType = KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core;

        KpiDetail kpiDetail = healKpis.get(healKpi.getIdentifier());

        if (kpiDetail != null) {
            String type = "";
            try {
                type = kpiTypes.get(Integer.parseInt(kpiDetail.getKpiTypeId()));
                kpiType = KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(type);
            } catch (Exception e) {
                log.error("KpiSubType: {} unavailable for subTypeId: {}. Defaulting KPI type to Core for jobId:{}, connector instance:{}", type, healKpi.getKpiTypeId(), jobId, connectorInstanceIdentifier, e);
            }
        }

        KPIAgentMessageProtos.KPIAgentMessage.KpiData laKpiData;

        if (src.getErrorPattern() != null) {
            laKpiData = KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder()
                    .setKpiUid(healKpi.getId())
                    .setKpiType(kpiType)
                    .setCollectionInterval(src.getCollectionInterval())
                    .setKpiName(healKpi.getName())
                    .setIsKpiGroup(false)
                    .setVal("null")
                    .setErrorCode(src.getErrorPattern() + ":" + errorProperty.getProperty(src.getErrorPattern()))
                    .setTimeInGMT(DateHelper.date2GMTString(src.getUpperThreshold())).build();
        } else {
            laKpiData = KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder()
                    .setKpiUid(healKpi.getId())
                    .setKpiType(kpiType)
                    .setCollectionInterval(src.getCollectionInterval())
                    .setKpiName(healKpi.getName())
                    .setIsKpiGroup(false)
                    .setVal(src.getValue() != null ? src.getValue().toString() : "null")
                    .setTimeInGMT(DateHelper.date2GMTString(src.getUpperThreshold())).build();
        }
        return laKpiData;
    }

    public KPIAgentMessageProtos.KPIAgentMessage.KpiData getGroupKpiData(DomainKpi src, KpiDetail healKpi) {
        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType kpiType = KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core;

        KpiDetail kpiDetail = healKpis.get(healKpi.getIdentifier());

        if (kpiDetail != null) {
            String type = "";
            try {
                type = kpiTypes.get(Integer.parseInt(kpiDetail.getKpiTypeId()));
                kpiType = KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(type);
            } catch (Exception e) {
                log.error("KpiSubType: {} unavailable for subTypeId: {}. Defaulting KPI type to Core for jobId:{}, connector instance:{}", type, kpiDetail.getKpiTypeId(), jobId, connectorInstanceIdentifier, e);
            }
        }

        KPIAgentMessageProtos.KPIAgentMessage.KpiData laKpiData;
        if (src.getGroupKpis() != null && !src.getGroupKpis().isEmpty()) {
            KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.Builder grpKpiBuilder = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder();
            grpKpiBuilder.putAllPairs(src.getGroupKpis());

            laKpiData = KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder()
                    .setKpiUid(healKpi.getId())
                    .setKpiType(kpiType)
                    .setCollectionInterval(src.getCollectionInterval())
                    .setKpiName(healKpi.getName())
                    .setIsKpiGroup(true)
                    .setGroupKpi(grpKpiBuilder.build())
                    .setKpiGroupName(src.getGroupName())
                    .setTimeInGMT(DateHelper.date2GMTString(src.getUpperThreshold())).build();
        } else {
            laKpiData = KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder()
                    .setKpiUid(healKpi.getId())
                    .setKpiType(kpiType)
                    .setCollectionInterval(src.getCollectionInterval())
                    .setKpiName(healKpi.getName())
                    .setIsKpiGroup(true)
                    .setKpiGroupName(src.getGroupName() != null ? src.getGroupName() : "null")
                    .setVal(src.getValue() != null ? src.getValue().toString() : "null")
                    .setTimeInGMT(DateHelper.date2GMTString(src.getUpperThreshold())).build();
        }
        return laKpiData;
    }
}
