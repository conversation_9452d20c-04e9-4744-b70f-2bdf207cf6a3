package com.heal.etladapter.transformers;

import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.HealAlert;
import com.heal.etladapter.pojos.ServiceNowTicket;
import com.heal.etladapter.utility.ServiceNowConstants;

import java.util.ArrayList;
import java.util.List;

public class HealAlertsToSnowTicketTransformer extends AbstractTransformer<List<HealAlert>, List<ServiceNowTicket>> {

	@Override
	public void initialize() throws EtlAdapterException {

	}

	@Override
	public List<ServiceNowTicket> transform(List<HealAlert> items) {

		List<ServiceNowTicket> serviceNowTickets = new ArrayList<>();

		items.forEach( alert -> {
				ServiceNowTicket snowTkt = new ServiceNowTicket();
				String summary = String.format(ServiceNowConstants.SERVICENOW_INCIDENT_SUMMARY, alert.getApplicationName());
				snowTkt.setSummary(summary);
				String desc = String.format(ServiceNowConstants.SERVICENOW_INCIDENT_DESC, alert.getApplicationName(),
						alert.getComponentInstanceName(), alert.getKpiName(), alert.getOperationType(), alert.getKpiValue(),
						alert.getUnits(), alert.getHealAlertId(), alert.getAlertTimestamp(), alert.getAccountName());
				snowTkt.setDescription(desc);
				String shortDesc = String.format(ServiceNowConstants.SERVICENOW_INCIDENT_SHORT_DESC,
						alert.getApplicationName(), alert.getHealAlertId(), alert.getAlertTimestamp(),
						alert.getAccountName());
				snowTkt.setShortDescription(shortDesc);
				snowTkt.setExternalSystem(ServiceNowConstants.SERVICENOW_NAME);
				snowTkt.setHealAlertId(alert.getHealAlertId());
				serviceNowTickets.add(snowTkt);
		});
		return serviceNowTickets;
	}

}
