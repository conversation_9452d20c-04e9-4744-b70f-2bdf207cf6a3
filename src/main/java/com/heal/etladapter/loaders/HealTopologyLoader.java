package com.heal.etladapter.loaders;

import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.beans.DomainHealInstance;
import com.heal.etladapter.beans.cc.*;
import com.heal.etladapter.exceptions.EtlAdapterException;
import com.heal.etladapter.pojos.HttpConnectionConfiguration;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.repo.redis.ServiceRepo;
import com.heal.etladapter.service.HttpConnection;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.etladapter.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Component
public class HealTopologyLoader extends AbstractLoader<List<CCPayload>> {

    @Autowired
    private AdapterHealthMetrics healthMetrics;
    @Autowired
    private ConnectorKpiMaster connectorKpiMaster;
    @Autowired
    private ServiceRepo serviceRepo;
    @Autowired
    protected ObjectMapper objectMapper;

    private String accessToken;
    private HttpConnection httpConnection;

    private final Set<String> entityMapping = new HashSet<>();
    private Map<String, DomainHealInstance> instanceMapping = new HashMap<>();
    private Map<String, Integer> serviceIdMapping;
    private int batchSize;
    List<DomainHealInstance> domainHealInstances = new ArrayList<>();

    @Override
    public void initialize() throws Exception {
        try {
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            accessToken = KeycloakConnectionManager.getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("Unable to connect to keycloak server for access token, so transaction creation call to CC wont happen for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
            }

            HttpConnectionConfiguration httpConnectionConfiguration = HttpConnectionConfiguration.builder()
                    .httpConnectionRequestTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_REQUEST_TIMEOUT, "5000")))
                    .httpClientConnectionTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_TIMEOUT, "5000")))
                    .httpSocketTimeout(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_SOCKET_TIMEOUT, "5000")))
                    .connectionKeepAliveTime(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_KEEP_ALIVE_TIME, "30000")))
                    .maxConnections(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS, "50")))
                    .maxConnectionsPerRoute(Integer.parseInt(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_MAX_CONNECTIONS_PER_ROUTE, "20")))
                    .disableSslValidation(Boolean.parseBoolean(this.parameters.getOrDefault(Constants.HTTP_CONNECTION_DISABLE_SSL_VALIDATION, "true")))
                    .build();
            httpConnection = new HttpConnection(httpConnectionConfiguration, healthMetrics);

            CloseableHttpClient client = httpConnection.getHttpConnection();
            if (client == null) {
                healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_INITIALIZATION_ERROR), 1);
                throw new EtlAdapterException("Unable to create HttpClient instance for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
            }

            reloadMapping();
            serviceIdMapping = new HashMap<>();
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_INITIALIZATION_ERROR, 1);
            log.error("Exception while initializing loader {} for jobId:{}, connector instance:{}", this.className, jobId, connectorInstanceIdentifier, e);
            throw new EtlAdapterException("Error in initialization for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (this.parameters.get(AdapterConstants.CC_INSTANCE_DELETE_URL) == null || this.parameters.get(AdapterConstants.CC_INSTANCE_DELETE_URL).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_CONFIGURATION_ERROR, 1);
            log.error("'controlcenter.instance.delete.url' configuration parameter unavailable for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.parameters);
            throw new EtlAdapterException("'controlcenter.instance.delete.url' configuration parameter unavailable for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (this.parameters.get(AdapterConstants.CC_SERVICE_ADD_URL) == null || this.parameters.get(AdapterConstants.CC_SERVICE_ADD_URL).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_CONFIGURATION_ERROR, 1);
            log.error("'controlcenter.service.add.url' configuration parameter unavailable or invalid for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.parameters);
            throw new EtlAdapterException("'controlcenter.service.add.url' configuration parameter unavailable or invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (this.parameters.get(AdapterConstants.CC_AGENT_ADD_URL) == null || this.parameters.get(AdapterConstants.CC_AGENT_ADD_URL).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_CONFIGURATION_ERROR, 1);
            log.error("'controlcenter.agent.add.url' configuration parameter unavailable or invalid for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.parameters);
            throw new EtlAdapterException("'controlcenter.agent.add.url' configuration parameter unavailable or invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (this.parameters.get(AdapterConstants.CC_INSTANCE_ADD_URL) == null || this.parameters.get(AdapterConstants.CC_INSTANCE_ADD_URL).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_CONFIGURATION_ERROR, 1);
            log.error("'controlcenter.instance.add.url' configuration parameter unavailable or invalid for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.parameters);
            throw new EtlAdapterException("'controlcenter.instance.add.url' configuration parameter unavailable or invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
        if (this.parameters.get(AdapterConstants.ACCOUNT_NAME) == null || this.parameters.get(AdapterConstants.ACCOUNT_NAME).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_CONFIGURATION_ERROR, 1);
            log.error("'account.name' configuration parameter unavailable or invalid for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.parameters);
            throw new EtlAdapterException("'account.name' configuration parameter unavailable or invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        if (this.parameters.get(AdapterConstants.CC_SERVICE_CONNECTION_URL) == null || this.parameters.get(AdapterConstants.CC_SERVICE_CONNECTION_URL).trim().isEmpty()) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_CONFIGURATION_ERROR, 1);
            log.error("'controlcenter.service.connection.url' configuration parameter unavailable or invalid for jobId:{}, connector instance:{}. Available worker_parameters: {}", jobId, connectorInstanceIdentifier, this.parameters);
            throw new EtlAdapterException("'controlcenter.service.connection.url' configuration parameter unavailable or invalid for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
        batchSize = this.parameters.get(AdapterConstants.BATCH_SIZE) != null ? Integer.parseInt(this.parameters.get(AdapterConstants.BATCH_SIZE)) : 10;
    }

    private void reloadMapping() {
        log.info("Inside reloadMapping() method.");
        try {
            domainHealInstances = connectorKpiMaster.fetchDomainEntityHealInstanceMapping(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
            domainHealInstances.forEach(ins -> {
                entityMapping.add(ins.getDomainInstanceName());
                entityMapping.add(ins.getHealAgentUid());
                entityMapping.addAll(ins.getHealServiceName());
            });
            instanceMapping = domainHealInstances.stream()
                    .collect(Collectors.toMap(DomainHealInstance::getHealInstanceName, Function.identity(), (r, n) -> r));
            log.trace("Instance mapping : {} for jobId:{}, connector instance:{}", instanceMapping, jobId, connectorInstanceIdentifier);
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.className, 1);
            log.error("Error occurred in reload mapping function for jobId:{}, connector instance:{}.", jobId, connectorInstanceIdentifier, e);
        }
    }

    @Override
    public Object load(List<CCPayload> ccPayloads) {
        if (ccPayloads.isEmpty()) {
            healthMetrics.putInLoaderErrors(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR, 1);
            log.warn("No entities identified in topology loader for jobId:{}, connector instance:{}.", jobId, connectorInstanceIdentifier);
            return null;
        }

        healthMetrics.putInLoaderReceivedCount(this.className, ccPayloads.size());

        log.info("Topology load received for jobId:{}, connector instance:{}, payloads: {}", jobId, connectorInstanceIdentifier, ccPayloads.size());

        // delete the entities from table and CC api
        deleteOldInstances(ccPayloads);

        reloadMapping();

        createServices(ccPayloads);

        createServiceConnections(ccPayloads);

        createAgents(ccPayloads);

        createHostInstances(ccPayloads);

        updateHostInstances(ccPayloads);

        createCompInstances(ccPayloads);

        return ccPayloads;
    }

    private void createCompInstances(List<CCPayload> ccPayloads) {
        String instanceCreateEndPoint = String.format(this.parameters.get(AdapterConstants.CC_INSTANCE_ADD_URL), this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        log.debug("Component instance endpoint: {}, jobId:{}, connector instance:{}", instanceCreateEndPoint, jobId, connectorInstanceIdentifier);
        // creating component instances
        List<CCPayload> compInstancePayloads = ccPayloads.stream().filter(e -> e.getEntityType().equalsIgnoreCase(AdapterConstants.COMP_INSTANCE))
                .collect(toList());

        if (compInstancePayloads.isEmpty()) {
            log.trace("No component instances to be created for jobId:{}, connector instance:{}. Overall CC payload: {}", jobId, connectorInstanceIdentifier, ccPayloads);
            return;
        }

        log.info("Component instance endpoint: {}, payload : {}, jobId:{}, connector instance:{}", instanceCreateEndPoint, compInstancePayloads.size(), jobId, connectorInstanceIdentifier);
        log.trace("Component instance for jobId:{}, connector instance:{}, payload : {}", compInstancePayloads, jobId, connectorInstanceIdentifier);
        List<List<CCPayload>> instancesInBatches = getInBatches(compInstancePayloads, batchSize);
        instancesInBatches.forEach(ins -> {
            List<HealInstancePayload> payloads = ins.stream().map(e -> (HealInstancePayload) e.getHealPayload()).filter(s -> !entityMapping.contains(s.getIdentifier())).toList();
            boolean compInstanceCreated = createHealInstance(payloads, instanceCreateEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.COMP_INSTANCE));

            if (!compInstanceCreated) {
                log.error("Heal component instance creation failed for jobId:{}, connector instance:{}. Endpoint: {}, component instances failed:{}", jobId, connectorInstanceIdentifier, instanceCreateEndPoint, payloads.size());
                return;
            }

            // update mapping table
            payloads.forEach(instance -> {
                connectorKpiMaster.addDomainEntities(instance.getIdentifier(), AdapterConstants.COMP_INSTANCE, this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
                connectorKpiMaster.updateDomainEntityHealInstanceMapper(instance.getName(), instance.getAgentIdentifiers().get(0), instance.getIdentifier(),
                        instance.getServiceIdentifiers().stream().findFirst().orElse(""), this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
            });
        });

        log.info("Instances created for jobId:{}, connector instance:{}, endpoint: {}, component instances discovered: {}", jobId, connectorInstanceIdentifier, instanceCreateEndPoint, compInstancePayloads.size());
    }

    private void createHostInstances(List<CCPayload> ccPayloads) {
        String instanceCreateEndPoint = String.format(this.parameters.get(AdapterConstants.CC_INSTANCE_ADD_URL), this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        log.info("Host instance create endpoint: {}, jobId:{}, connector instance:{}", instanceCreateEndPoint, jobId, connectorInstanceIdentifier);

        // creating host instances
        List<CCPayload> hostInstancePayloads = ccPayloads.stream()
                .filter(e -> e.getEntityType().equalsIgnoreCase(AdapterConstants.HOST_INSTANCE))
                .collect(toList());

        if (hostInstancePayloads.isEmpty()) {
            log.trace("No host instances to be created for jobId:{}, connector instance:{}. Overall CC Payload: {}", jobId, connectorInstanceIdentifier, ccPayloads);
            return;
        }

        log.debug("Host instance endpoint: {}, payload : {}, jobId:{}, connector instance:{}", instanceCreateEndPoint, hostInstancePayloads.size(), jobId, connectorInstanceIdentifier);
        log.trace("Host instance payload : {}, jobId:{}, connector instance:{}", hostInstancePayloads, jobId, connectorInstanceIdentifier);

        List<List<CCPayload>> hostsInBatches = getInBatches(hostInstancePayloads, batchSize);

        hostsInBatches.forEach(ht -> {
            List<HealInstancePayload> payloads = ht.stream().map(e -> (HealInstancePayload) e.getHealPayload())
                    .filter(s -> !entityMapping.contains(s.getIdentifier())).toList();

            boolean healInstanceCreated = createHealInstance(payloads, instanceCreateEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HOST_INSTANCE));

            if (!healInstanceCreated) {
                log.error("Heal host instance creation failed for jobId:{}, connector instance:{}. Endpoint:{}, host instances failed :{}", jobId, connectorInstanceIdentifier, instanceCreateEndPoint, payloads.size());
                healthMetrics.putInLoaderErrors(this.className, 1);
                return;
            }

            // update mapping table
            payloads.forEach(instance -> {
                connectorKpiMaster.addDomainEntities(instance.getIdentifier(), AdapterConstants.HOST_INSTANCE, this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
                instance.getAgentIdentifiers().forEach(agent -> connectorKpiMaster.updateDomainEntityHealInstanceMapper(instance.getName(), agent, instance.getIdentifier(),
                        String.join(",", instance.getServiceIdentifiers()), this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate));
            });
        });
        log.info("Host instances created! for jobId:{}, connector instance:{}, endpoint: {}, host instances discovered: {}", jobId, connectorInstanceIdentifier, instanceCreateEndPoint, hostInstancePayloads.size());
    }

    private void updateHostInstances(List<CCPayload> ccPayloads) {
        String instanceUpdateEndPoint = String.format(this.parameters.get(AdapterConstants.CC_INSTANCE_ADD_URL), this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        log.info("Host instance update endpoint: {} for jobId:{}, connector instance:{}", instanceUpdateEndPoint, jobId, connectorInstanceIdentifier);

        // updating host instances
        List<HealInstanceUpdatePayload> hostInstancePayloads = ccPayloads.stream().filter(e -> e.getEntityType().equalsIgnoreCase(AdapterConstants.HOST_INSTANCE_UPDATE))
                .map(e -> (HealInstanceUpdatePayload) e.getHealPayload())
                .collect(Collectors.toList());

        if (hostInstancePayloads.isEmpty()) {
            log.trace("No host instances to be updated for jobId:{}, connector instance:{}. Overall CC payload: {}", jobId, connectorInstanceIdentifier, ccPayloads);
            return;
        }

        log.info("Host instance update endpoint: {}, payload : {}, jobId:{}, connector instance:{}", instanceUpdateEndPoint, hostInstancePayloads.size(), jobId, connectorInstanceIdentifier);
        log.trace("Host instance update, payload : {}, jobId:{}, connector instance:{}", hostInstancePayloads.size(), jobId, connectorInstanceIdentifier);

        boolean healInstanceUpdated = updateInstances(hostInstancePayloads, instanceUpdateEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HOST_INSTANCE_UPDATE));

        log.info("Host instances updated endpoint: {}, instances modified: {}, jobId:{}, connector instance:{}", instanceUpdateEndPoint, hostInstancePayloads.size(), jobId, connectorInstanceIdentifier);

        if (!healInstanceUpdated) {
            log.error("Heal host instance update failed for for jobId:{}, connector instance:{}, endpoint: {}", jobId, connectorInstanceIdentifier, instanceUpdateEndPoint);
            healthMetrics.putInLoaderErrors(this.className, 1);
            return;
        }

        // update mapping table
        hostInstancePayloads.forEach(instance -> {
            Set<String> services = instance.getApplication().get(0).getService().stream().map(ServicePojo::getIdentifier).collect(Collectors.toSet());
            if (instanceMapping.containsKey(instance.getInstanceIdentifier())) {
                services.addAll(instanceMapping.get(instance.getInstanceIdentifier()).getHealServiceName());
            }

            Set<String> existingServices = domainHealInstances.stream()
                    .filter(d -> d.getHealServiceName() != null && d.getHealServiceName().stream().anyMatch(services::contains))
                    .map(DomainHealInstance::getHealServiceName)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());

            if (!existingServices.isEmpty()) {
                connectorKpiMaster.updateHealServicesInHealInstances(instance.getInstanceIdentifier(), String.join(",", existingServices), this.jdbcTemplate);
            }
        });

        log.info("Host instance updated! for jobId:{}, connector instance:{}, endpoint: {}, service discovered: {}", jobId, connectorInstanceIdentifier, instanceUpdateEndPoint, hostInstancePayloads.size());
    }

    private void createAgents(List<CCPayload> ccPayloads) {
        String agentEndPoint = String.format(this.parameters.get(AdapterConstants.CC_AGENT_ADD_URL), this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        log.info("Heal agents creation  : url : {}, jobId:{}, connector instance:{}", agentEndPoint, jobId, connectorInstanceIdentifier);
        List<HealAgentPayload> agentPayloads = ccPayloads.stream()
                .filter(e -> e.getEntityType().equalsIgnoreCase(AdapterConstants.AGENT))
                .map(e -> (HealAgentPayload) e.getHealPayload())
                .filter(s -> !entityMapping.contains(s.getUniqueToken()))
                .collect(Collectors.toList());

        if (agentPayloads.isEmpty()) {
            log.trace("No agents to be created for jobId:{}, connector instance:{}. Overall CC payloads: {}", jobId, connectorInstanceIdentifier, ccPayloads);
            return;
        }
        List<BasicEntity> accountWiseServices = serviceRepo.getAccountWiseServices(this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        if (accountWiseServices == null) {
            log.error("No services available to create agents for jobId:{}, connector instance:{}, account : {}", jobId, connectorInstanceIdentifier, this.parameters.get(AdapterConstants.ACCOUNT_NAME));
            return;
        }
        serviceIdMapping = accountWiseServices.stream().collect(toMap(BasicEntity::getIdentifier, BasicEntity::getId, (r, v) -> r));
        log.trace("Service mapping  details for jobId:{}, connector instance:{}, services : {}", jobId, connectorInstanceIdentifier, serviceIdMapping);

        log.info("Agent creation endpoint: {}, payload: {}, jobId:{}, connector instance:{}", agentEndPoint, agentPayloads.size(), jobId, connectorInstanceIdentifier);
        log.trace("Heal Agent payload : {}, jobId:{}, connector instance:{}", agentPayloads, jobId, connectorInstanceIdentifier);

        agentPayloads.forEach(agent -> {
            try {
                if (!serviceIdMapping.containsKey(agent.getServiceName())) {
                    log.error("Service mapped to the agent not available for jobId:{}, connector instance:{}, agent : {}", jobId, connectorInstanceIdentifier, agent);
                    return;
                }
                agent.setServiceId(serviceIdMapping.get(agent.getServiceName()));

                boolean healInstanceCreated = createHealAgent(agent, agentEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.AGENT));
                if (!healInstanceCreated) {
                    log.error("Heal agent creation failed for jobId:{}, connector instance:{}, Endpoint:{}, agents discovered: {}", jobId, connectorInstanceIdentifier, agentEndPoint, agentPayloads.size());
                    healthMetrics.putInLoaderErrors(this.className, 1);
                }
            } catch (Exception e) {
                log.error("Error in creating agents from CC for jobId:{}, connector instance:{}, details: {}", jobId, connectorInstanceIdentifier, agentPayloads, e);
                healthMetrics.putInLoaderErrors(this.className, 1);
            }
        });
    }

    private void createServices(List<CCPayload> ccPayloads) {
        String serviceCreateEndPoint = String.format(this.parameters.get(AdapterConstants.CC_SERVICE_ADD_URL), this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        log.info("Heal services creation : url : {}, jobId:{}, connector instance:{} ", serviceCreateEndPoint, jobId, connectorInstanceIdentifier);
        List<CCPayload> servicePayloads = ccPayloads.stream().filter(e -> e.getEntityType().equalsIgnoreCase(AdapterConstants.SERVICE))
                .collect(toList());

        if (servicePayloads.isEmpty()) {
            log.trace("No services to be created for jobId:{}, connector instance:{}. Overall CC payloads: {}", jobId, connectorInstanceIdentifier, ccPayloads);
            return;
        }
        log.info("Service endpoint: {}, payload : {}, jobId:{}, connector instance:{}", serviceCreateEndPoint, servicePayloads.size(), jobId, connectorInstanceIdentifier);
        log.trace("Service payload : {}, jobId:{}, connector instance:{}", servicePayloads, jobId, connectorInstanceIdentifier);
        List<List<CCPayload>> serviceInBatches = getInBatches(servicePayloads, batchSize);
        serviceInBatches.forEach(svs -> {
            List<HealServicePayload> payloads = svs.stream().map(e -> (HealServicePayload) e.getHealPayload())
                    .filter(s -> !entityMapping.contains(s.getIdentifier())).toList();

            boolean serviceCreated = createHealService(payloads, serviceCreateEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.SERVICE));
            if (!serviceCreated) {
                log.error("Heal service creation failed for jobId:{}, connector instance:{}, Endpoint: {}, services: {}", jobId, connectorInstanceIdentifier, serviceCreateEndPoint, payloads);
                healthMetrics.putInLoaderErrors(this.className, 1);
            }
        });
        log.info("Services created successfully! for jobId:{}, connector instance:{}, endpoint: {}, service discovered: {}", jobId, connectorInstanceIdentifier, serviceCreateEndPoint, servicePayloads.size());
    }

    private void deleteOldInstances(List<CCPayload> ccPayloads) {
        List<HealInstanceDeletePayload> delInstances = ccPayloads.stream()
                .filter(e -> e.getEntityType().equalsIgnoreCase(AdapterConstants.DELETE_INSTANCE))
                .map(e -> (HealInstanceDeletePayload) e.getHealPayload())
                .toList();

        if (delInstances.isEmpty()) {
            log.trace("No heal instances to be deleted for jobId:{}, connector instance:{}. Overall CC payloads: {}", jobId, connectorInstanceIdentifier, ccPayloads);
            return;
        }

        List<String> instanceIdentifiers = delInstances.stream().map(HealInstanceDeletePayload::getInstanceId).toList();

        String deleteInstanceEndPoint = String.format(this.parameters.get(AdapterConstants.CC_INSTANCE_DELETE_URL), this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        deleteInstanceEndPoint = deleteInstanceEndPoint.concat(String.join(",", instanceIdentifiers));

        log.info("Number of deletable entities identified at loader for jobId:{}, connector instance:{}, count:{}, endpoint: {}", jobId, connectorInstanceIdentifier, delInstances.size(), deleteInstanceEndPoint);

        boolean instancesDeleted = deleteHealInstance(instanceIdentifiers.size(), deleteInstanceEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.DELETE_INSTANCE));
        if (!instancesDeleted) {
            log.error("Heal instances deletion failed for jobId:{}, connector instance:{}, Endpoint: {}", jobId, connectorInstanceIdentifier, deleteInstanceEndPoint);
            healthMetrics.putInLoaderErrors(this.className, 1);
            return;
        }

        connectorKpiMaster.deleteDomainEntityHealInstanceMapper(instanceIdentifiers, this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
        connectorKpiMaster.deleteDomainEntities(instanceIdentifiers, this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
    }

    private void createServiceConnections(List<CCPayload> ccPayloads) {
        String serviceConnectionEndPoint = String.format(this.parameters.get(AdapterConstants.CC_SERVICE_CONNECTION_URL), this.parameters.get(AdapterConstants.ACCOUNT_NAME));
        log.info("Service connection endpoint: {} for jobId:{}, connector instance:{}", serviceConnectionEndPoint, jobId, connectorInstanceIdentifier);

        List<CCPayload> serviceConnectionPayloads = ccPayloads.stream()
                .filter(e -> e.getEntityType().equalsIgnoreCase(AdapterConstants.SERVICE_CONNECTION))
                .collect(toList());

        if (serviceConnectionPayloads.isEmpty()) {
            log.trace("No service connections to be created for jobId:{}, connector instance:{}, Details: {}", jobId, connectorInstanceIdentifier, ccPayloads);
            return;
        }
        Set<String> accountWiseServices = serviceRepo.getAccountWiseServices(this.parameters.get(AdapterConstants.ACCOUNT_NAME)).stream().map(BasicEntity::getIdentifier).collect(Collectors.toSet());

        log.info("Service connection endpoint: {}, payload : {}, jobId:{}, connector instance:{}", serviceConnectionEndPoint, serviceConnectionPayloads.size(), jobId, connectorInstanceIdentifier);
        log.trace("Service connection payload : {}, jobId:{}, connector instance:{}", serviceConnectionPayloads, jobId, connectorInstanceIdentifier);
        List<List<CCPayload>> serviceConnectionPayloadBatches = getInBatches(serviceConnectionPayloads, batchSize);
        serviceConnectionPayloadBatches.forEach(scp -> {
            List<HealServiceConnectionPayload> scPayloads = scp.stream().map(e -> (HealServiceConnectionPayload) e.getHealPayload())
                    .filter(sd -> accountWiseServices.contains(sd.getSourceServiceIdentifier()) &&
                            accountWiseServices.contains(sd.getDestinationServiceIdentifier())).collect(toList());
            boolean serviceConnectionCreated = createServiceConnection(scPayloads, serviceConnectionEndPoint, this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.SERVICE_CONNECTION));
            if (!serviceConnectionCreated) {
                log.error("Heal service connection creation failed for jobId:{}, connector instance:{}, Endpoint: {}, services connection failed payload: {}", jobId, connectorInstanceIdentifier, serviceConnectionEndPoint, scPayloads);
                healthMetrics.putInLoaderErrors(this.className, 1);
            }
        });
        log.info("Service connection created! for jobId:{}, connector instance:{}, endpoint: {} , service connections discovered: {}", jobId, connectorInstanceIdentifier, serviceConnectionEndPoint, serviceConnectionPayloads.size());
    }

    private boolean createHealInstance(List<HealInstancePayload> discoveredInstances, String endPointUrl, String instanceIdentifierType) {
        try {
            String jsonPayload = objectMapper.writeValueAsString(discoveredInstances);

            Map<String, String> header = new HashMap<>();
            header.put(AdapterConstants.AUTHORIZATION_HEADER_NAME, accessToken);
            header.put(AdapterConstants.CONTENT_TYPE_HEADER_NAME, AdapterConstants.CONTENT_TYPE_JSON);

            log.info("Trying to create discovered instances for jobId:{}, connector instance:{}, count:{}, requestURL :{}, payload :{}", jobId, connectorInstanceIdentifier, discoveredInstances.size(), endPointUrl, jsonPayload);

            String tokenResponse = httpConnection.httpPost(endPointUrl, jsonPayload, header, instanceIdentifierType);

            if (tokenResponse == null) {
                healthMetrics.putInLoaderDropCount(instanceIdentifierType, discoveredInstances.size());
                log.error("Error in creation of instances:{} and received null response for jobId:{}, connector instance:{}", discoveredInstances.size(), jobId, connectorInstanceIdentifier);
                return false;
            }

            InstanceResponse response = objectMapper.reader().forType(new TypeReference<InstanceResponse>() {
            }).readValue(tokenResponse);

            if (response == null || !response.getResponseStatus().equals("SUCCESS")) {
                healthMetrics.putInLoaderDropCount(instanceIdentifierType, discoveredInstances.size());
                log.error("Failed to create the heal instances for jobId:{}, connector instance:{}, resp: {}", jobId, connectorInstanceIdentifier, tokenResponse);
                return false;
            }

            log.info("Successfully created instances for jobId:{}, connector instance:{}, resp: {}", jobId, connectorInstanceIdentifier, response);

            healthMetrics.putInLoaderProcessedCount(instanceIdentifierType, discoveredInstances.size());

            return true;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR), 1);
            log.error("Exception while creating a new heal instances for jobId:{}, connector instance:{}, count: {}, endPointUrl:{}", jobId, connectorInstanceIdentifier, discoveredInstances.size(), endPointUrl, e);
        }
        return false;
    }

    private boolean createHealService(List<HealServicePayload> discoveredServices, String endPointUrl, String instanceIdentifierType) {
        try {
            String jsonPayload = objectMapper.writeValueAsString(discoveredServices);

            Map<String, String> header = new HashMap<>();
            header.put(AdapterConstants.AUTHORIZATION_HEADER_NAME, accessToken);
            header.put(AdapterConstants.CONTENT_TYPE_HEADER_NAME, AdapterConstants.CONTENT_TYPE_JSON);

            log.info("Trying to create discovered services for jobId:{}, connector instance:{}, count:{}, requestURL :{}, payload :{}", jobId, connectorInstanceIdentifier, discoveredServices.size(), endPointUrl, jsonPayload);

            String tokenResponse = httpConnection.httpPost(endPointUrl, jsonPayload, header, instanceIdentifierType);

            if (tokenResponse == null) {
                healthMetrics.putInLoaderDropCount(instanceIdentifierType, discoveredServices.size());
                log.error("Error in creation of services:{} and received null response for jobId:{}, connector instance:{}", discoveredServices.size(), jobId, connectorInstanceIdentifier);
                return false;
            }

            InstanceResponse response = objectMapper.reader().forType(new TypeReference<InstanceResponse>() {
            }).readValue(tokenResponse);

            if (response == null || !response.getResponseStatus().equals("SUCCESS")) {
                healthMetrics.putInLoaderDropCount(instanceIdentifierType, discoveredServices.size());
                log.info("Failed to create the heal service for jobId:{}, connector instance:{}, resp: {}", jobId, connectorInstanceIdentifier, tokenResponse);
                return false;
            }

            log.info("Successfully created services for jobId:{}, connector instance:{}, resp: {}", jobId, connectorInstanceIdentifier, response);

            healthMetrics.putInLoaderProcessedCount(instanceIdentifierType, discoveredServices.size());

            return true;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR), 1);
            log.error("Exception while creating new heal services for jobId:{}, connector instance:{}, count : {}, endPointUrl:{}", jobId, connectorInstanceIdentifier, discoveredServices.size(), endPointUrl, e);
        }

        return false;
    }

    public boolean createHealAgent(HealAgentPayload agentPayload, String endPointUrl, String instanceIdentifierType) {
        try {
            String jsonPayload = objectMapper.writeValueAsString(agentPayload);

            Map<String, String> header = new HashMap<>();
            header.put(AdapterConstants.AUTHORIZATION_HEADER_NAME, accessToken);
            header.put(AdapterConstants.CONTENT_TYPE_HEADER_NAME, AdapterConstants.CONTENT_TYPE_JSON);

            log.info("Trying to create agent for jobId:{}, connector instance:{}, count:{}, requestURL :{}, payload :{}", jobId, connectorInstanceIdentifier, agentPayload.getName(), endPointUrl, jsonPayload);

            String tokenResponse = httpConnection.httpPost(endPointUrl, jsonPayload, header, instanceIdentifierType);

            if (tokenResponse == null) {
                log.error("Error in creation of heal agent identifier:{} and received null response for jobId:{}, connector instance:{} ", agentPayload.getName(), jobId, connectorInstanceIdentifier);
                return false;
            }

            ResponseObject response = objectMapper.reader().forType(new TypeReference<ResponseObject>() {
            }).readValue(tokenResponse);

            if (response == null || !response.getResponseStatus().equals("SUCCESS")) {
                healthMetrics.putInLoaderDropCount(instanceIdentifierType, 1);
                log.info("Failed to create the agent for jobId:{}, connector instance:{}, agent : {}", jobId, connectorInstanceIdentifier, agentPayload.getName());
                return false;
            }
            log.info("Successfully created the agent for jobId:{}, connector instance:{}, resp: {}", jobId, connectorInstanceIdentifier, response);

            healthMetrics.putInLoaderProcessedCount(instanceIdentifierType, 1);

            return true;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR), 1);
            log.error("Exception in creation of heal agent for jobId:{}, connector instance:{}, identifier:  {}", jobId, connectorInstanceIdentifier, agentPayload.getName(), e);
        }
        return false;
    }

    public boolean deleteHealInstance(int instanceCount, String endPointUrl, String instanceIdentifierType) {
        try {
            String tokenResponse = httpConnection.httpDelete(endPointUrl, Map.of(AdapterConstants.AUTHORIZATION_HEADER_NAME, accessToken), instanceIdentifierType);

            if (tokenResponse == null) {
                log.error("Error in creation of heal agent identifier and received null response for URL: {}, jobId:{}, connector instance:{} ", endPointUrl, jobId, connectorInstanceIdentifier);
                return false;
            }

            ResponseObject response = objectMapper.reader().forType(new TypeReference<ResponseObject>() {
            }).readValue(tokenResponse);

            if (response == null || !response.getResponseStatus().equals("SUCCESS")) {
                healthMetrics.putInLoaderDropCount(instanceIdentifierType, instanceCount);
                log.info("Failed to delete instances using the URL: {} for jobId:{}, connector instance:{}", endPointUrl, jobId, connectorInstanceIdentifier);
                return false;
            }
            log.info("Successfully deleted instances for jobId:{}, connector instance:{} using URL: {}", jobId, connectorInstanceIdentifier, endPointUrl);

            healthMetrics.putInLoaderProcessedCount(instanceIdentifierType, instanceCount);

            return true;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR), 1);
            log.error("Exception in deleting instances for jobId:{}, connector instance:{}, url:{}", jobId, connectorInstanceIdentifier, endPointUrl, e);
        }
        return false;
    }

    public boolean updateInstances(List<HealInstanceUpdatePayload> modifiedInstances, String endPointUrl, String instanceIdentifierType) {
        try {
            String jsonPayload = objectMapper.writeValueAsString(modifiedInstances);

            Map<String, String> header = new HashMap<>();
            header.put(AdapterConstants.AUTHORIZATION_HEADER_NAME, accessToken);
            header.put(AdapterConstants.CONTENT_TYPE_HEADER_NAME, AdapterConstants.CONTENT_TYPE_JSON);

            log.info("Trying to update modified instances for jobId:{}, connector instance:{}, count:{}, requestURL :{}, payload :{}", jobId, connectorInstanceIdentifier, modifiedInstances.size(), endPointUrl, jsonPayload);

            String tokenResponse = httpConnection.httpPut(endPointUrl, jsonPayload, header, instanceIdentifierType);

            if (tokenResponse == null) {
                healthMetrics.putInLoaderDropCount("HealTopologyLoader", modifiedInstances.size());
                log.error("Error in update of instances for jobId:{}, connector instance:{}, count:{} and received null response", jobId, connectorInstanceIdentifier, modifiedInstances.size());
                return false;
            }

            InstanceResponse response = objectMapper.reader().forType(new TypeReference<InstanceResponse>() {
            }).readValue(tokenResponse);

            if (response == null || !response.getResponseStatus().equals("SUCCESS")) {
                healthMetrics.putInLoaderDropCount("HealTopologyLoader_Instances", modifiedInstances.size());
                log.error("Failed to update the heal instances for jobId:{}, connector instance:{}, resp: {}", jobId, connectorInstanceIdentifier, tokenResponse);
                return false;
            }

            log.info("Successfully updated instances for jobId:{}, connector instance:{}, count:{}", jobId, connectorInstanceIdentifier, response);

            healthMetrics.putInLoaderProcessedCount(instanceIdentifierType, 1);

            return true;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR), 1);
            log.error("Exception while updating heal instances for jobId:{}, connector instance:{}, size: {}, endPointUrl:{}", jobId, connectorInstanceIdentifier, modifiedInstances.size(), endPointUrl, e);
        }
        return false;
    }

    public boolean createServiceConnection(List<HealServiceConnectionPayload> connectionPayloads, String endPointUrl, String instanceIdentifierType) {
        try {
            String jsonPayload = objectMapper.writeValueAsString(connectionPayloads);

            Map<String, String> header = new HashMap<>();
            header.put(AdapterConstants.AUTHORIZATION_HEADER_NAME, accessToken);
            header.put(AdapterConstants.CONTENT_TYPE_HEADER_NAME, AdapterConstants.CONTENT_TYPE_JSON);

            log.info("Trying to create service connections for jobId:{}, connector instance:{}, size: {}, requestURL :{}, payload :{}", jobId, connectorInstanceIdentifier, connectionPayloads.size(), endPointUrl, jsonPayload);
            String tokenResponse = httpConnection.httpPost(endPointUrl, jsonPayload, header, instanceIdentifierType);

            if (tokenResponse == null) {
                healthMetrics.putInLoaderDropCount("HealTopologyLoader", connectionPayloads.size());
                log.error("Error in creation of heal service connection for jobId:{}, connector instance:{} and received null response : {} ", jobId, connectorInstanceIdentifier, connectionPayloads);
                return false;
            }
            InstanceResponse response = objectMapper.reader().forType(new TypeReference<InstanceResponse>() {
            }).readValue(tokenResponse);

            if (response == null || !response.getResponseStatus().equals("SUCCESS")) {
                log.info("Failed to create service connection for jobId:{}, connector instance:{}, resp : {}", jobId, connectorInstanceIdentifier, response);
                return false;
            }
            log.info("Successfully created the service connection for jobId:{}, connector instance:{}, resp : {}", jobId, connectorInstanceIdentifier, response);

            healthMetrics.putInLoaderProcessedCount(instanceIdentifierType, connectionPayloads.size());

            return true;
        } catch (Exception e) {
            healthMetrics.putInLoaderErrors(this.connectorInstanceIdentifier.concat(":").concat(AdapterConstants.HEAL_TOPOLOGY_LOADER_ERROR), 1);
            log.error("Exception in creation of heal service connection for jobId:{}, connector instance:{}, URL: {}, payload: {}", jobId, connectorInstanceIdentifier, endPointUrl, connectionPayloads, e);
        }
        return false;
    }

    private List<List<CCPayload>> getInBatches(List<CCPayload> payloads, int batchSize) {
        return IntStream.range(0, (payloads.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> payloads.subList(i * batchSize, Math.min((i + 1) * batchSize, payloads.size())))
                .collect(Collectors.toList());
    }
}
