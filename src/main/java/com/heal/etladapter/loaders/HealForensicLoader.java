package com.heal.etladapter.loaders;

import com.appnomic.appsone.common.protbuf.A1EventProtos;
import com.heal.etladapter.utility.AdapterConstants;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class HealForensicLoader extends MessageQueueLoader<A1EventProtos.A1Event> {

    @Override
    public Object load(A1EventProtos.A1Event a1Event) {
        try {
            Connection mqConn = tLocalConnection.get();
            Channel mqChannel = tLocalChannel.get();
            if (mqConn == null || !mqConn.isOpen()) {
                log.info("Connection not open. Attempting to re-connect");
                mqChannel = tLocalChannel.get();
            }
            log.debug("Object {} loaded to Message Queue", a1Event);

            // If exchange key is not available, then send it directly to queue.
            if (this.parameters.containsKey(AdapterConstants.MQ_CONFIG_EXCHANGE)) {
                mqChannel.basicPublish(this.parameters.get(AdapterConstants.MQ_CONFIG_EXCHANGE),
                        this.parameters.get(AdapterConstants.MQ_CONFIG_ROUTINGKEY), null, a1Event.toByteArray());
            } else {
                mqChannel.basicPublish("",
                        this.parameters.get(AdapterConstants.MQ_CONFIG_QUEUE), null, a1Event.toByteArray());
            }
        } catch (IOException e) {
            log.error("IOException occurred when loading {}", a1Event, e);
        } catch (NullPointerException e) {
            log.error("NullPointerException occurred when loading {}", a1Event, e);
        }

        return a1Event;
    }
}
