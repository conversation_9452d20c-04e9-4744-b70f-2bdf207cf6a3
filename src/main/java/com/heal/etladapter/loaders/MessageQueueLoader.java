package com.heal.etladapter.loaders;

import com.heal.etladapter.utility.AdapterConstants;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class MessageQueueLoader<U> extends AbstractLoader<U> {

    ConnectionFactory mqConnFactory;

    final ThreadLocal<Connection> tLocalConnection = ThreadLocal.withInitial(() -> {
        Connection mqConn;
        try {
            mqConn = mqConnFactory.newConnection();
        } catch (Exception e) {
            mqConn = null;
            log.error(String.valueOf(e));
        }
        return mqConn;
    });

    final ThreadLocal<Channel> tLocalChannel = ThreadLocal.withInitial(() -> {
        Channel channel;
        try {
            channel = tLocalConnection.get().createChannel();
        } catch (Exception e) {
            channel = null;
            log.error(String.valueOf(e));
        }
        if (channel != null) {
            log.debug(channel.toString());
        }
        return channel;
    });

    @Override
    public void initialize() throws Exception {

        if (this.parameters == null || this.parameters.isEmpty()) {
            log.error("Parameters missing for class {}. Re-check configuration file.",
                    MessageQueueLoader.class.getName());
            throw new Exception("Initialization Parameters missing for MQ Loader");
        }
        if (!this.parameters.containsKey(AdapterConstants.MQ_CONFIG_HOST)) {
            log.error("Parameter {} missing for class {}. Re-check configuration file.",
                    AdapterConstants.MQ_CONFIG_HOST, MessageQueueLoader.class.getName());
            throw new Exception("Initialization Parameter [mq-host] missing for MQ Loader");
        }

        if (!this.parameters.containsKey(AdapterConstants.MQ_CONFIG_EXCHANGE)) {
            log.error("Parameter {} missing for class {}. Re-check configuration file.",
                    AdapterConstants.MQ_CONFIG_EXCHANGE, MessageQueueLoader.class.getName());
            throw new Exception("Initialization Parameter [mq-exchange] missing for MQ Loader");
        }

        if (!this.parameters.containsKey(AdapterConstants.MQ_CONFIG_ROUTINGKEY)) {
            log.error("Parameter {} missing for class {}. Re-check configuration file.",
                    AdapterConstants.MQ_CONFIG_ROUTINGKEY, MessageQueueLoader.class.getName());
            throw new Exception("Initialization Parameter [mq-routing-key] missing for MQ Loader");
        }

        mqConnFactory = new ConnectionFactory();
        mqConnFactory.setHost(this.parameters.get(AdapterConstants.MQ_CONFIG_HOST));
        if (this.parameters.containsKey(AdapterConstants.MQ_CONFIG_PORT)) {
            mqConnFactory.setPort(Integer.parseInt(this.parameters.get(AdapterConstants.MQ_CONFIG_PORT)));
        }
        if (this.parameters.containsKey(AdapterConstants.MQ_CONFIG_SSL_ENABLED) && Boolean.parseBoolean(this.parameters.get(AdapterConstants.MQ_CONFIG_SSL_ENABLED))) {
            mqConnFactory.useSslProtocol();
        }
    }
}
