package com.heal.etladapter.utility;

public class Constants {
    public static final String METRIC_API = "/api/v2/metrics/query";
    public static final String TRANS_API = "/api/v2/metrics/query?metricSelector=builtin:service.keyRequest.(response.time,count.total,errors.fourxx.count)";
    public static final String AUTHORIZATION = "Authorization";
    public static final String DT_SERVICE_URL = "dt.service.url";
    public static final String DT_ENTITY_URL = "dt.entity.url";
    public static final String DT_EVENT_URL = "dt.event.url";
    public static final String DT_EVENT_NEXT_PAGE_URL = "dt.event.nextpageurl";
    public static final String DT_TRANS_URL = "dt.transaction.url";
    public static final String DT_KPI_URL = "dt.kpi.url";

    public static final String MANAGEMENT_ZONE_ID = "management.zone.id";
    public static final String REMOVE_INSTANCES = "remove.instances";

    public static final String METRIC_NAME = "metric.name";

    public static final String ACTUAL_VALUE = "actual.value";

    public static final String THRESHOULD_VALUE = "threshold.value";

    public static final String METRIC_ATTRIBUTE = "metric.attribute";

    public static final String API_TOKEN = "Api-Token ";
    public static final String RESOLUTION = "&resolution=";
    public static final String FROM = "&from=";

    public static final String METRIC_SELECTOR = "dt.event.metric_selector";

    public static final String THRESHOLD_SELECTOR = "dt.event.metric_threshold";

    public static final String DESCRIPTION_SELECTOR = "dt.event.description";
    public static final String GROUP_LABEL = "dt.event.group_label";

    public static final String TO = "&to=";
    public static final String STATUS = "status";

    public static final String CHAIN_REPO = "dynatraceadapter";

    public static final String ENTITY_API = "/api/v2/entities";
    public static final String EVENTS_API = "/api/v2/events";
    public static final String HOST = "HOST";
    public static final String SERVICE = "SERVICE";
    public static final String ENTITIES = "entities";
    public static final String EVENTS = "events";
    public static final String ENTITY_ID = "entityId";

    public static final String EVENT_METRIC_COLLECTION = "event.metric.collection";
    public static final String ERROR_PERCENTILE_VALUE = "dt.event.baseline.response_time_p90";
    public static final String ERROR_PERCENTILE_REFERENCE = "dt.event.baseline.response_time_p90_reference";
    public static final String ERROR_RATE = "dt.event.baseline.error_rate";
    public static final String ERROR_RATE_REFERENCE = "dt.event.baseline.error_rate_reference";
    public static final String EVENT_SELECTOR = "&eventSelector=";

    public static final String MANAGEMENT_ZN_ID = "managementZoneId(";
    public static final String ENTITY_MZ_ID = "mzId";

    public static final String ID = "id";
    public static final String TYPE = "type";
    public static final String HOST_TYPE = "HOST";
    public static final int PAGE_SIZE = 1000;
    public static final String PROCESS_GROUP_INSTANCE = "PROCESS_GROUP_INSTANCE";

    public static final String DEFAULT_KPI_IDENTIFIER = "default.kpi.identifier";
    public static final String DEFAULT_KPI_ID = "default.kpi.id";

    public static final String KPI_THREAD_POOL_MAX_SIZE = "kpi.thread.pool.max.size";
    public static final String KPI_THREAD_POOL_CORE_SIZE = "kpi.thread.pool.core.size";
    public static final String KPI_THREAD_POOL_QUEUE_CAPACITY = "kpi.thread.pool.queue.capacity";

    public static final String EVENT_THREAD_POOL_MAX_SIZE = "event.thread.pool.max.size";
    public static final String EVENT_THREAD_POOL_CORE_SIZE = "event.thread.pool.core.size";
    public static final String EVENT_THREAD_POOL_QUEUE_CAPACITY = "event.thread.pool.queue.capacity";

    public static final String TRANSACTION_THREAD_POOL_MAX_SIZE = "transaction.thread.pool.max.size";
    public static final String TRANSACTION_THREAD_POOL_CORE_SIZE = "transaction.thread.pool.core.size";
    public static final String TRANSACTION_THREAD_POOL_QUEUE_CAPACITY = "transaction.thread.pool.queue.capacity";

    public static final String EVENTS_KPI_REGEX = "dt.events.kpi.regex";

    public static final String EVENT_TXN_REGEX = "dt.events.txn.regex";
    public static final String EVENT_EXTRACTOR_ERRORS = "EventExtractorErrors";
    public static final String EVENT_EXTRACTOR_CONFIGURATION_ERROR = "EventExtractorConfigurationError";
    public static final String EVENT_EXTRACTOR_INITIALIZE_ERROR = "EventExtractorInitializeError";
    public static final String EVENT_METRIC_IDENTIFIER_ERRORS = "EventExtractorMetricIdentifierErrors";
    public static final String DYNATRACE_KPI_EXTRACTOR_ERRORS = "DynatraceKpiExtractorErrors";
    public static final String DYNATRACE_KPI_EXTRACTOR_CONFIGURATION_ERROR = "DynatraceKpiExtractorConfigurationError";
    public static final String DYNATRACE_KPI_EXTRACTOR_INITIALIZE_ERROR = "DynatraceKpiExtractorInitializeError";

    public static final String ENTUITY_KPI_EXTRACTOR_ERRORS = "EntuityKpiExtractorErrors";
    public static final String ENTUITY_KPI_EXTRACTOR_CONFIGURATION_ERROR = "EntuityKpiExtractorConfigurationError";
    public static final String ENTUITY_KPI_EXTRACTOR_INITIALIZE_ERROR = "EntuityKpiExtractorInitializeError";

    public static final String TOPOLOGY_EXTRACTOR_ERRORS = "TopologyExtractorErrors";
    public static final String TOPOLOGY_SERVICE_EXTRACTOR_ERRORS = "TopologyServiceExtractorErrors";
    public static final String TOPOLOGY_HOST_EXTRACTOR_ERRORS = "TopologyHostExtractorErrors";
    public static final String TOPOLOGY_PG_EXTRACTOR_ERRORS = "TopologyProcessGroupExtractorErrors";
    public static final String TOPOLOGY_TRANSFORMER_CONFIGURATION_ERROR = "TopologyTransformerConfigurationError";
    public static final String TOPOLOGY_EXTRACTOR_INITIALIZE_ERROR = "TopologyExtractorInitializeError";
    public static final String TOPOLOGY_TRANSFORMER_INITIALIZE_ERROR = "TopologyTransformerInitializeError";
    public static final String TOPOLOGY_TRANSFORMER_ERRORS = "TopologyTransformerErrors";
    public static final String TOPOLOGY_EXTRACTOR_CONFIGURATION_ERROR = "TopologyExtractorConfigurationError";

    public static final String TRANSACTION_EXTRACTOR_ERRORS = "TransactionExtractorErrors";
    public static final String TRANSACTION_EXTRACTOR_INITIALIZE_ERROR = "TransactionExtractorInitializeError";
    public static final String METRIC_DATA_NOT_FOUND = "METRIC DATA NOT FOUND";

    public static final String DEFAULT_AGENT = "default.agent";
    public static final String DEFAULT_GRPC_ADDRESS = "haproxy-node1.appnomic";
    public static final String DEFAULT_GRPC_PORT = "9998";
    public static final String DEFAULT_HOST_COMPONENT = "Dynatrace";
    public static final String DEFAULT_INSTANCE_COMPONENT = "Dynatrace JVM App";
    public static final String DEFAULT_COMPONENT_VERSION = "ALL";

    public static final String ADAPTER_ENTUITY_REPOSITORY = "entuity_repository";
    public static final String SERVER_URL = "url";
    public static final String SERVER_USER = "username";
    public static final String SERVER_PASSWORD = "password";

    public static final String DEVICE_IP = "IpAddress";
    public static final String INSTANCE_FIELD = "instance.field";
    public static final String DEVICE_SYS_NAME = "SystemName";

    public static final String GROUP_ATTRIBUTE_FIELD = "group.attribute.field";
    public static final String KPI_BATCH_SIZE = "batch.size";
    public static final String TOPOLOGY_APPLICATION_IDENTIFIER = "topology.application.identifier";
    public static final String TOPOLOGY_TIME_ZONE = "topology.time.zone";

    public static final String HTTP_CONNECTION_REQUEST_TIMEOUT = "http.client.connection.request.timeout";
    public static final String HTTP_CONNECTION_SOCKET_TIMEOUT = "http.client.socket.timeout";
    public static final String HTTP_CONNECTION_TIMEOUT = "http.client.connection.timeout";
    public static final String HTTP_CONNECTION_MAX_CONNECTIONS = "http.client.max.connections";
    public static final String HTTP_CONNECTION_MAX_CONNECTIONS_PER_ROUTE = "http.client.max.connections.per.route";
    public static final String HTTP_CONNECTION_KEEP_ALIVE_TIME = "http.connection.keep.alive.time";
    public static final String HTTP_CONNECTION_DISABLE_SSL_VALIDATION = "http.connection.disable.ssl.validation";

    public static final String FORENSIC_OUTPUT = "FORENSIC_OUTPUT";
}
