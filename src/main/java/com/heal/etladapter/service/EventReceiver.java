package com.heal.etladapter.service;

import com.heal.configuration.protbuf.ScheduledJobProtos;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.config.RabbitMqConfig;
import com.heal.etladapter.core.DataCollectionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@RabbitListener(queues = "${connector.message.queue.name}")
public class EventReceiver<S, T, U> {

    @Autowired
    protected RabbitMqConfig rabbitMqConfig;

    @Autowired
    protected DataCollectionHandler<S, T, U> dataCollectionHandler;

    @Autowired
    protected AdapterHealthMetrics healthMetrics;

    @RabbitHandler
    public void receive(byte[] scheduledJobProto) {
        try {
            healthMetrics.updateRmqMessagesReceivedCount(1);
            ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.parseFrom(scheduledJobProto);
            log.trace("Connector received event: {}", scheduledJob);

            dataCollectionHandler.process(scheduledJob);
        } catch (Exception e) {
            log.error("Exception in receiving event from incoming queue: {}", rabbitMqConfig.connectorMessagesQueue, e);
            healthMetrics.updateRmqMessagesDropCount();
        }
    }
}
