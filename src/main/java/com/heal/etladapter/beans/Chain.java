package com.heal.etladapter.beans;

import com.heal.etladapter.extractors.AbstractExtractor;
import com.heal.etladapter.loaders.AbstractLoader;
import com.heal.etladapter.transformers.AbstractTransformer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Chain<S, T, U> {

    private String connectorInstanceIdentifier;
    private String chainIdentifier;
    private boolean status;
    private List<AbstractExtractor<S, T>> extractor;
    private List<AbstractTransformer<T, U>> transformer;
    private List<AbstractLoader<U>> loader;
}
